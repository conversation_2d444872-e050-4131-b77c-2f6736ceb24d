/**
 * Floating Clipboard Widget Component
 * Displays connected device clipboard content with tap-to-copy functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Dimensions,
  Platform
} from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { floatingOverlayService, ClipboardItem } from '../services/FloatingOverlayService';

interface FloatingClipboardWidgetProps {
  visible: boolean;
  onClose: () => void;
  onCopy?: (content: string) => void;
}

const FloatingClipboardWidget: React.FC<FloatingClipboardWidgetProps> = ({
  visible,
  onClose,
  onCopy
}) => {
  const [clipboardItems, setClipboardItems] = useState<ClipboardItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<ClipboardItem | null>(null);

  useEffect(() => {
    if (visible) {
      // Load clipboard items from the service
      const items = floatingOverlayService.getClipboardItems();
      setClipboardItems(items);
    }
  }, [visible]);

  const handleCopyToClipboard = async (item: ClipboardItem) => {
    try {
      await Clipboard.setStringAsync(item.content);
      onCopy?.(item.content);
      
      Alert.alert(
        'Copied!',
        `Content from ${item.deviceName} copied to clipboard`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      Alert.alert('Error', 'Failed to copy content to clipboard');
    }
  };

  const handleItemPress = (item: ClipboardItem) => {
    setSelectedItem(item);
  };

  const handleQuickCopy = async (item: ClipboardItem) => {
    await handleCopyToClipboard(item);
    // Auto-close after copying
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'windows': return '🖥️';
      case 'android': return '📱';
      case 'linux': return '🐧';
      default: return '🖥️';
    }
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.widget}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>📋 PC & Server Clipboards</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Clipboard Items */}
          <ScrollView style={styles.itemsList} showsVerticalScrollIndicator={false}>
            {clipboardItems.length === 0 ? (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>No PC/Server clipboard content</Text>
                <Text style={styles.emptyStateSubtext}>
                  Connect to Windows PCs or Linux servers to see their clipboard content here
                </Text>
              </View>
            ) : (
              clipboardItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.clipboardItem}
                  onPress={() => handleItemPress(item)}
                  onLongPress={() => handleQuickCopy(item)}
                >
                  <View style={styles.itemHeader}>
                    <View style={styles.deviceInfo}>
                      <Text style={styles.deviceIcon}>
                        {getDeviceIcon(item.deviceType)}
                      </Text>
                      <View style={styles.deviceDetails}>
                        <Text style={styles.deviceName}>{item.deviceName}</Text>
                        <Text style={styles.timestamp}>{item.timestamp}</Text>
                      </View>
                    </View>
                    <TouchableOpacity
                      style={styles.copyButton}
                      onPress={() => handleCopyToClipboard(item)}
                    >
                      <Text style={styles.copyButtonText}>📋</Text>
                    </TouchableOpacity>
                  </View>
                  
                  <Text style={styles.itemContent}>
                    {truncateText(item.content)}
                  </Text>
                  
                  <Text style={styles.tapHint}>
                    Tap to preview • Long press to quick copy
                  </Text>
                </TouchableOpacity>
              ))
            )}
          </ScrollView>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickActionButton}>
              <Text style={styles.quickActionText}>🔄 Refresh</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickActionButton}>
              <Text style={styles.quickActionText}>⚙️ Settings</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Item Detail Modal */}
        {selectedItem && (
          <Modal
            visible={!!selectedItem}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setSelectedItem(null)}
          >
            <View style={styles.detailOverlay}>
              <View style={styles.detailModal}>
                <View style={styles.detailHeader}>
                  <Text style={styles.detailTitle}>
                    {getDeviceIcon(selectedItem.deviceType)} {selectedItem.deviceName}
                  </Text>
                  <TouchableOpacity onPress={() => setSelectedItem(null)}>
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={styles.detailContent}>
                  <Text style={styles.detailText}>{selectedItem.content}</Text>
                </ScrollView>
                
                <View style={styles.detailActions}>
                  <TouchableOpacity
                    style={styles.detailCopyButton}
                    onPress={() => {
                      handleCopyToClipboard(selectedItem);
                      setSelectedItem(null);
                    }}
                  >
                    <Text style={styles.detailCopyButtonText}>📋 Copy to Clipboard</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        )}
      </View>
    </Modal>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  widget: {
    backgroundColor: '#1E1E1E',
    borderRadius: 16,
    width: Math.min(width - 40, 350),
    maxHeight: height * 0.7,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#E0E0E0',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#E0E0E0',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemsList: {
    maxHeight: 300,
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#A0A0A0',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#808080',
    textAlign: 'center',
  },
  clipboardItem: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  deviceIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  deviceDetails: {
    flex: 1,
  },
  deviceName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E0E0E0',
  },
  timestamp: {
    fontSize: 12,
    color: '#A0A0A0',
  },
  copyButton: {
    backgroundColor: '#8B5CF6',
    borderRadius: 6,
    padding: 6,
    minWidth: 30,
    alignItems: 'center',
  },
  copyButtonText: {
    fontSize: 14,
  },
  itemContent: {
    fontSize: 14,
    color: '#C0C0C0',
    lineHeight: 20,
    marginBottom: 4,
  },
  tapHint: {
    fontSize: 11,
    color: '#808080',
    fontStyle: 'italic',
  },
  quickActions: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#333',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: '#333',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  quickActionText: {
    color: '#E0E0E0',
    fontSize: 14,
    fontWeight: '600',
  },
  // Detail Modal Styles
  detailOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  detailModal: {
    backgroundColor: '#1E1E1E',
    borderRadius: 16,
    width: '100%',
    maxHeight: height * 0.8,
  },
  detailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#E0E0E0',
  },
  detailContent: {
    padding: 16,
    maxHeight: 300,
  },
  detailText: {
    fontSize: 16,
    color: '#E0E0E0',
    lineHeight: 24,
  },
  detailActions: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  detailCopyButton: {
    backgroundColor: '#8B5CF6',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  detailCopyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FloatingClipboardWidget;
