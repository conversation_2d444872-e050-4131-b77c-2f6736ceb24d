import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as SplashScreen from 'expo-splash-screen';
import { LogBox } from 'react-native';

// Import Android App Version 1 - Production Clipsy app
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './ClippyApp';

// Ignore specific warnings to prevent noise in development
LogBox.ignoreLogs([
  'Animated: `useNativeDriver` was not specified',
  'Non-serializable values were found in the navigation state',
]);

console.log('App.tsx: Android App Version 1 starting...');

export default function App() {
  console.log('App.tsx: Android App Version 1 component mounted');

  useEffect(() => {
    console.log('App.tsx: Android App Version 1 useEffect running');

    // Force hide splash screen immediately
    const hideSplash = async () => {
      try {
        console.log('App.tsx: Force hiding splash screen');
        await SplashScreen.hideAsync();
        console.log('App.tsx: Splash screen hidden successfully');
      } catch (e) {
        console.warn('App.tsx: Error hiding splash screen:', e);
      }
    };

    // Hide splash screen immediately
    hideSplash();
  }, []);

  console.log('App.tsx: Rendering Android App Version 1');

  return (
    <SafeAreaProvider>
      <StatusBar style="light" />
      <ClippyApp />
    </SafeAreaProvider>
  );
}
