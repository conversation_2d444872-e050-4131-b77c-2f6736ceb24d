{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@components/*": ["components/*"], "@utils/*": ["utils/*"], "@screens/*": ["screens/*"], "@services/*": ["services/*"], "@common/*": ["../common/*"]}, "jsx": "react-native", "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "moduleResolution": "bundler", "isolatedModules": true, "noEmit": true, "target": "esnext", "lib": ["esnext"], "allowSyntheticDefaultImports": true}, "include": ["**/*.ts", "**/*.tsx", "../common/**/*.ts"], "exclude": ["node_modules/**/*", "babel.config.js", "metro.config.js", "jest.config.js"]}