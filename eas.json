{"cli": {"version": ">= 7.8.6", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk"}, "ios": {"simulator": true}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "env": {"NODE_ENV": "production"}}, "production": {"android": {"buildType": "app-bundle"}}, "production-apk": {"extends": "production", "android": {"buildType": "apk"}, "env": {"NODE_ENV": "production"}}, "standalone": {"distribution": "internal", "android": {"buildType": "apk"}, "env": {"NODE_ENV": "production", "EXPO_PUBLIC_ENV": "production"}}, "simple": {"distribution": "internal", "android": {"buildType": "apk"}}}, "submit": {"production": {}}}