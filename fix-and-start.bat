@echo off
echo Starting ClipBee mobile app with extra compatibility options...

:: Kill any existing Metro processes that might be running
echo Checking for existing Metro processes...
taskkill /f /im node.exe /fi "WINDOWTITLE eq Metro*" >nul 2>&1

:: Set environment variables to bypass TypeScript setup issues
set EXPO_NO_TYPESCRIPT_SETUP=1
set NODE_OPTIONS=--openssl-legacy-provider
set EXPO_DEBUG=true

:: Clear Expo cache and run with specific host options
echo Starting with clean cache and specific host...
npx expo-cli start --clear --no-dev --host lan
