/**
 * Permissions Service for Clipsy Android App
 * Handles all app permissions including camera, network, background tasks
 */

import { Alert, Linking, Platform } from 'react-native';

// Platform-specific imports
let BarCodeScanner: any = null;
let BackgroundFetch: any = null;
let TaskManager: any = null;
let Notifications: any = null;

if (Platform.OS !== 'web') {
  try {
    BarCodeScanner = require('expo-barcode-scanner').BarCodeScanner;
    BackgroundFetch = require('expo-background-fetch');
    TaskManager = require('expo-task-manager');
    Notifications = require('expo-notifications');
  } catch (error) {
    console.warn('Native modules not available on this platform:', error);
  }
}

export interface PermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

export interface AllPermissions {
  camera: PermissionStatus;
  backgroundFetch: PermissionStatus;
  notifications: PermissionStatus;
  networkState: PermissionStatus;
}

export interface PermissionsCallbacks {
  onPermissionGranted: (permission: string) => void;
  onPermissionDenied: (permission: string) => void;
  onAllPermissionsGranted: () => void;
  onCriticalPermissionDenied: (permission: string) => void;
}

class PermissionsService {
  private callbacks: PermissionsCallbacks | null = null;

  /**
   * Initialize permissions service
   */
  initialize(callbacks: PermissionsCallbacks) {
    this.callbacks = callbacks;
  }

  /**
   * Request all necessary permissions for the app
   */
  async requestAllPermissions(): Promise<AllPermissions> {
    console.log('Requesting all permissions...');

    const permissions: AllPermissions = {
      camera: await this.requestCameraPermission(),
      backgroundFetch: await this.requestBackgroundFetchPermission(),
      notifications: await this.requestNotificationPermission(),
      networkState: await this.requestNetworkStatePermission()
    };

    // Check if all critical permissions are granted
    const criticalPermissions = ['camera', 'backgroundFetch'];
    const allCriticalGranted = criticalPermissions.every(
      perm => permissions[perm as keyof AllPermissions].granted
    );

    if (allCriticalGranted) {
      this.callbacks?.onAllPermissionsGranted();
    } else {
      // Find which critical permissions were denied
      criticalPermissions.forEach(perm => {
        if (!permissions[perm as keyof AllPermissions].granted) {
          this.callbacks?.onCriticalPermissionDenied(perm);
        }
      });
    }

    return permissions;
  }

  /**
   * Request camera permission for QR code scanning
   */
  async requestCameraPermission(): Promise<PermissionStatus> {
    if (Platform.OS === 'web' || !BarCodeScanner) {
      console.log('Camera permission not available on this platform');
      this.callbacks?.onPermissionDenied('camera');
      return {
        granted: false,
        canAskAgain: false,
        status: 'unavailable'
      };
    }

    try {
      console.log('Requesting camera permission...');

      const { status, canAskAgain } = await BarCodeScanner.requestPermissionsAsync();
      
      const permissionStatus: PermissionStatus = {
        granted: status === 'granted',
        canAskAgain,
        status
      };

      if (permissionStatus.granted) {
        console.log('Camera permission granted');
        this.callbacks?.onPermissionGranted('camera');
      } else {
        console.log('Camera permission denied:', status);
        this.callbacks?.onPermissionDenied('camera');
        
        if (!canAskAgain) {
          this.showPermissionDeniedAlert('Camera', 'QR code scanning for device pairing');
        }
      }

      return permissionStatus;
    } catch (error) {
      console.error('Failed to request camera permission:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'error'
      };
    }
  }

  /**
   * Request background fetch permission for clipboard sync
   */
  async requestBackgroundFetchPermission(): Promise<PermissionStatus> {
    try {
      console.log('Requesting background fetch permission...');

      // For web environment, background fetch is always available
      if (Platform.OS === 'web' || !BackgroundFetch) {
        console.log('Background fetch permission granted (web environment)');
        this.callbacks?.onPermissionGranted('backgroundFetch');
        return {
          granted: true,
          canAskAgain: true,
          status: 'granted'
        };
      }

      const status = await BackgroundFetch.getStatusAsync();

      const permissionStatus: PermissionStatus = {
        granted: status === BackgroundFetch.BackgroundFetchStatus.Available,
        canAskAgain: true,
        status: status ? this.getBackgroundFetchStatusString(status) : 'unknown'
      };

      if (permissionStatus.granted) {
        console.log('Background fetch permission granted');
        this.callbacks?.onPermissionGranted('backgroundFetch');
      } else {
        console.log('Background fetch permission denied:', status);
        this.callbacks?.onPermissionDenied('backgroundFetch');

        if (status === BackgroundFetch.BackgroundFetchStatus.Denied) {
          this.showPermissionDeniedAlert('Background App Refresh', 'automatic clipboard synchronization');
        }
      }

      return permissionStatus;
    } catch (error) {
      console.error('Failed to request background fetch permission:', error);

      // For web environment, fallback to granted
      if (Platform.OS === 'web') {
        console.log('Background fetch permission granted (web fallback)');
        this.callbacks?.onPermissionGranted('backgroundFetch');
        return {
          granted: true,
          canAskAgain: true,
          status: 'granted'
        };
      }

      return {
        granted: false,
        canAskAgain: false,
        status: 'error'
      };
    }
  }

  /**
   * Request notification permission for sync alerts
   */
  async requestNotificationPermission(): Promise<PermissionStatus> {
    if (Platform.OS === 'web' || !Notifications) {
      console.log('Notification permission granted (web environment)');
      this.callbacks?.onPermissionGranted('notifications');
      return {
        granted: true,
        canAskAgain: true,
        status: 'granted'
      };
    }

    try {
      console.log('Requesting notification permission...');

      if (!Notifications) {
        console.warn('Notifications module not available');
        return {
          granted: false,
          canAskAgain: false,
          status: 'unavailable'
        };
      }

      // Use modern expo-notifications API
      const { status } = await Notifications.requestPermissionsAsync();

      const permissionStatus: PermissionStatus = {
        granted: status === 'granted',
        canAskAgain: status !== 'denied',
        status
      };

      if (permissionStatus.granted) {
        console.log('Notification permission granted');
        this.callbacks?.onPermissionGranted('notifications');
      } else {
        console.log('Notification permission denied:', status);
        this.callbacks?.onPermissionDenied('notifications');
      }

      return permissionStatus;
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'error'
      };
    }
  }

  /**
   * Request network state permission for connectivity monitoring
   */
  async requestNetworkStatePermission(): Promise<PermissionStatus> {
    try {
      console.log('Requesting network state permission...');
      
      // Network state permission is usually granted by default on Android
      // This is more of a check than a request
      const permissionStatus: PermissionStatus = {
        granted: true, // Usually granted by default
        canAskAgain: true,
        status: 'granted'
      };

      console.log('Network state permission granted (default)');
      this.callbacks?.onPermissionGranted('networkState');

      return permissionStatus;
    } catch (error) {
      console.error('Failed to check network state permission:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'error'
      };
    }
  }

  /**
   * Check current status of all permissions
   */
  async checkAllPermissions(): Promise<AllPermissions> {
    console.log('Checking all permissions status...');

    const permissions: AllPermissions = {
      camera: await this.checkCameraPermission(),
      backgroundFetch: await this.checkBackgroundFetchPermission(),
      notifications: await this.checkNotificationPermission(),
      networkState: await this.checkNetworkStatePermission()
    };

    return permissions;
  }

  /**
   * Check camera permission status
   */
  async checkCameraPermission(): Promise<PermissionStatus> {
    if (Platform.OS === 'web' || !BarCodeScanner) {
      return { granted: false, canAskAgain: false, status: 'unavailable' };
    }

    try {
      const { status, canAskAgain } = await BarCodeScanner.getPermissionsAsync();

      return {
        granted: status === 'granted',
        canAskAgain,
        status
      };
    } catch (error) {
      console.error('Failed to check camera permission:', error);
      return { granted: false, canAskAgain: false, status: 'error' };
    }
  }

  /**
   * Check background fetch permission status
   */
  async checkBackgroundFetchPermission(): Promise<PermissionStatus> {
    try {
      // For web environment, background fetch is always available
      if (Platform.OS === 'web' || !BackgroundFetch) {
        return {
          granted: true,
          canAskAgain: true,
          status: 'granted'
        };
      }

      const status = await BackgroundFetch.getStatusAsync();

      return {
        granted: status === BackgroundFetch.BackgroundFetchStatus.Available,
        canAskAgain: true,
        status: status ? this.getBackgroundFetchStatusString(status) : 'unknown'
      };
    } catch (error) {
      console.error('Failed to check background fetch permission:', error);

      // For web environment, fallback to granted
      if (Platform.OS === 'web') {
        return {
          granted: true,
          canAskAgain: true,
          status: 'granted'
        };
      }

      return { granted: false, canAskAgain: false, status: 'error' };
    }
  }

  /**
   * Check notification permission status
   */
  async checkNotificationPermission(): Promise<PermissionStatus> {
    if (Platform.OS === 'web' || !Notifications) {
      return { granted: true, canAskAgain: true, status: 'granted' };
    }

    try {
      const { status } = await Notifications.getPermissionsAsync();

      return {
        granted: status === 'granted',
        canAskAgain: status !== 'denied',
        status
      };
    } catch (error) {
      console.error('Failed to check notification permission:', error);
      return { granted: false, canAskAgain: false, status: 'error' };
    }
  }

  /**
   * Check network state permission status
   */
  async checkNetworkStatePermission(): Promise<PermissionStatus> {
    // Network state is usually available by default
    return {
      granted: true,
      canAskAgain: true,
      status: 'granted'
    };
  }

  /**
   * Show alert when permission is permanently denied
   */
  private showPermissionDeniedAlert(permissionName: string, feature: string) {
    // For web environment, use window.alert
    if (Platform.OS === 'web') {
      const message = `${permissionName} Permission Required\n\nClipsy needs ${permissionName.toLowerCase()} access for ${feature}. Please enable it in your browser settings.`;
      window.alert(message);
      return;
    }

    Alert.alert(
      `${permissionName} Permission Required`,
      `Clipsy needs ${permissionName.toLowerCase()} access for ${feature}. Please enable it in your device settings.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Settings',
          onPress: () => {
            if (Platform.OS === 'ios') {
              Linking.openURL('app-settings:');
            } else {
              Linking.openSettings();
            }
          }
        }
      ]
    );
  }

  /**
   * Convert background fetch status to string
   */
  private getBackgroundFetchStatusString(status: any): string {
    if (!BackgroundFetch) return 'unknown';

    switch (status) {
      case BackgroundFetch.BackgroundFetchStatus?.Available:
        return 'available';
      case BackgroundFetch.BackgroundFetchStatus?.Denied:
        return 'denied';
      case BackgroundFetch.BackgroundFetchStatus?.Restricted:
        return 'restricted';
      default:
        return 'unknown';
    }
  }

  /**
   * Get permission summary for display
   */
  async getPermissionSummary(): Promise<string[]> {
    const permissions = await this.checkAllPermissions();
    const summary: string[] = [];

    Object.entries(permissions).forEach(([key, permission]) => {
      const status = permission.granted ? '✅' : '❌';
      const name = key.charAt(0).toUpperCase() + key.slice(1);
      summary.push(`${status} ${name}: ${permission.status}`);
    });

    return summary;
  }

  /**
   * Request specific permission by name
   */
  async requestPermission(permissionName: keyof AllPermissions): Promise<PermissionStatus> {
    switch (permissionName) {
      case 'camera':
        return await this.requestCameraPermission();
      case 'backgroundFetch':
        return await this.requestBackgroundFetchPermission();
      case 'notifications':
        return await this.requestNotificationPermission();
      case 'networkState':
        return await this.requestNetworkStatePermission();
      default:
        throw new Error(`Unknown permission: ${permissionName}`);
    }
  }

  /**
   * Check if all critical permissions are granted
   */
  async areAllCriticalPermissionsGranted(): Promise<boolean> {
    const permissions = await this.checkAllPermissions();
    return permissions.camera.granted && permissions.backgroundFetch.granted;
  }

  /**
   * Get list of missing critical permissions
   */
  async getMissingCriticalPermissions(): Promise<string[]> {
    const permissions = await this.checkAllPermissions();
    const missing: string[] = [];

    if (!permissions.camera.granted) missing.push('camera');
    if (!permissions.backgroundFetch.granted) missing.push('backgroundFetch');

    return missing;
  }
}

// Export singleton instance
export const permissionsService = new PermissionsService();
export default PermissionsService;
