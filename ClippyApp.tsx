import React, { useState, lazy, Suspense } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  FlatList,
  StatusBar,
  ActivityIndicator,
  Modal,
  Alert,
  TextInput,
  Platform,
  Clipboard
} from 'react-native';
// Temporarily disable clipboard import to fix native module issues
// import Clipboard from '@react-native-clipboard/clipboard';
// Temporarily disable OptimizedIcon to fix Android issues
// import OptimizedIcon from './components/OptimizedIcon';

// Simple fallback icon component
const SimpleIcon = ({ name, size = 24, color = '#FFFFFF', fallbackText }: {
  name: string;
  size?: number;
  color?: string;
  fallbackText?: string;
}) => {
  const iconMap: Record<string, string> = {
    'sync': '🔄',
    'settings': '⚙️',
    'edit': '✏️',
    'copy': '📋',
    'add': '+',
    'close': '✕',
    'chevron-right': '>',
  };

  return (
    <Text style={{ fontSize: size, color }}>
      {iconMap[name] || fallbackText || '?'}
    </Text>
  );
};
// import ClipboardTestScreen from './screens/ClipboardTestScreen';

// Temporarily disable DemoClipsyInterface to fix Android issues
// import DemoClipsyInterface from './src/components/DemoClipsyInterface';

// Simplified data structure without requiring imported types
interface ClipItem {
  id: string;
  content: string;
  timestamp: Date;
}

// Sample data
const SAMPLE_ITEMS: ClipItem[] = [
  {
    id: '1',
    content: 'This is an older clipboard item. It\'s shorter.',
    timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
  },
  {
    id: '2',
    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',
    timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
  },
  {
    id: '3',
    content: 'Yet another historical entry.',
    timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
  },
  {
    id: '4',
    content: 'Some code snippet: function hello() { console.log("Hello World!"); }',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
  },
];

// Lazy load the clipboard item component
const ClipboardItem = lazy(() => Promise.resolve({
  default: ({ item, onPress }: { item: ClipItem; onPress: (item: ClipItem) => void }) => {
  const [expanded, setExpanded] = useState(false);
  
  const timeSince = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";
    
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";
    
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";
    
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";
    
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";
    
    return Math.floor(seconds) + " seconds ago";
  };
  
  return (
    <TouchableOpacity 
      style={styles.historyItem}
      onPress={() => {
        setExpanded(!expanded);
        onPress(item);
      }}
    >
      <Text style={styles.timestamp}>{timeSince(item.timestamp)}</Text>
      <Text 
        style={[styles.itemContent, expanded && styles.expandedContent]}
        numberOfLines={expanded ? undefined : 1}
      >
        {item.content}
      </Text>
    </TouchableOpacity>
  );
}}));

// Main app component
export default function ClippyApp() {
  const [connected, setConnected] = useState(false);
  const [connectedDevicesCount, setConnectedDevicesCount] = useState(0);
  const [clipboardItems, setClipboardItems] = useState<ClipItem[]>(SAMPLE_ITEMS);
  const [liveClipboardText, setLiveClipboardText] = useState(
    'This is the latest text synced from your PC. It might be a long string that needs to be displayed properly and then can be copied or edited.'
  );
  const [showTestScreen, setShowTestScreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDevicePairing, setShowDevicePairing] = useState(false);
  const [editingText, setEditingText] = useState('');
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      setConnected(prev => {
        const newConnected = !prev;
        setConnectedDevicesCount(newConnected ? Math.floor(Math.random() * 3) + 1 : 0);
        return newConnected;
      });
    }, 5000);

    return () => clearInterval(interval);
  }, []);
  
  const handleItemPress = (item: ClipItem) => {
    setLiveClipboardText(item.content);
  };

  // Settings button handler
  const handleSettingsPress = () => {
    setShowSettings(true);
  };

  // Device pairing handler
  const handleDevicePairingPress = () => {
    setShowSettings(false);
    setShowDevicePairing(true);
  };

  const handleClipboardChange = (content: string) => {
    setLiveClipboardText(content);
  };

  // Edit button handler
  const handleEditPress = () => {
    setEditingText(liveClipboardText);
    setShowEditModal(true);
  };

  // Copy button handler
  const handleCopyPress = async () => {
    try {
      if (Platform.OS === 'web') {
        // For web platform, use navigator.clipboard
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(liveClipboardText);
          Alert.alert('Success', 'Text copied to clipboard!');
        } else {
          // Fallback for older browsers
          const textArea = document.createElement('textarea');
          textArea.value = liveClipboardText;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          Alert.alert('Success', 'Text copied to clipboard!');
        }
      } else {
        // For mobile platforms - use React Native's built-in Clipboard
        Clipboard.setString(liveClipboardText);
        Alert.alert('Success', 'Text copied to clipboard!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to copy text to clipboard');
      console.error('Copy error:', error);
    }
  };

  // Create new note handler
  const handleCreateNotePress = () => {
    const newItem: ClipItem = {
      id: Date.now().toString(),
      content: liveClipboardText,
      timestamp: new Date(),
    };
    setClipboardItems(prev => [newItem, ...prev]);
    Alert.alert('Success', 'Added to clipboard history!');
  };

  // Sync button handler
  const handleSyncPress = () => {
    Alert.alert('Sync', 'Syncing with paired devices...', [
      { text: 'OK', onPress: () => console.log('Sync initiated') }
    ]);
  };

  // Save edited text
  const handleSaveEdit = () => {
    setLiveClipboardText(editingText);
    setShowEditModal(false);
    Alert.alert('Success', 'Text updated!');
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#121212" />
      
      {/* Clipboard Test Screen Modal */}
      <Modal
        visible={showTestScreen}
        animationType="slide"
        onRequestClose={() => setShowTestScreen(false)}
      >
        <SafeAreaView style={{flex: 1}}>
          <View style={styles.testScreenHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowTestScreen(false)}
            >
              <SimpleIcon name="close" size={24} color="#FFFFFF" fallbackText="✕" />
            </TouchableOpacity>
          </View>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ color: '#FFFFFF', fontSize: 18 }}>Clipboard Test Screen</Text>
            <Text style={{ color: '#A0A0A0', marginTop: 10 }}>Feature temporarily disabled</Text>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        animationType="slide"
        onRequestClose={() => setShowSettings(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Settings</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowSettings(false)}
            >
              <SimpleIcon name="close" size={24} color="#FFFFFF" fallbackText="✕" />
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.settingItem} onPress={handleDevicePairingPress}>
              <Text style={styles.settingText}>Device Pairing</Text>
              <SimpleIcon name="chevron-right" size={20} color="#A0A0A0" fallbackText=">" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.settingItem}>
              <Text style={styles.settingText}>Sync Settings</Text>
              <SimpleIcon name="chevron-right" size={20} color="#A0A0A0" fallbackText=">" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.settingItem}>
              <Text style={styles.settingText}>Clear History</Text>
              <SimpleIcon name="chevron-right" size={20} color="#A0A0A0" fallbackText=">" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.settingItem}>
              <Text style={styles.settingText}>About</Text>
              <SimpleIcon name="chevron-right" size={20} color="#A0A0A0" fallbackText=">" />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Edit Text Modal */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        onRequestClose={() => setShowEditModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Edit Text</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowEditModal(false)}
            >
              <SimpleIcon name="close" size={24} color="#FFFFFF" fallbackText="✕" />
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            <TextInput
              style={styles.editTextInput}
              value={editingText}
              onChangeText={setEditingText}
              multiline
              placeholder="Enter your text here..."
              placeholderTextColor="#A0A0A0"
            />
            <View style={styles.editActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowEditModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveEdit}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Device Pairing Modal - Temporarily Disabled */}


      
      {/* Main App Interface */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={[styles.statusDot, connected ? styles.connected : styles.disconnected]} />
          <Text style={styles.title}>Clipsy</Text>
          {connected && connectedDevicesCount > 0 && (
            <Text style={styles.deviceCount}> ({connectedDevicesCount} devices)</Text>
          )}
        </View>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity style={styles.iconButton} onPress={handleSyncPress}>
            <SimpleIcon name="sync" size={24} color="#FFFFFF" fallbackText="🔄" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={handleSettingsPress}>
            <SimpleIcon name="settings" size={24} color="#FFFFFF" fallbackText="⚙️" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Live Clipboard Preview */}
      <View style={styles.livePreview}>
        <Text style={styles.liveText}>{liveClipboardText}</Text>
        <View style={styles.actionIcons}>
          <TouchableOpacity onPress={handleEditPress}>
            <SimpleIcon name="edit" size={20} color="#A0A0A0" fallbackText="✏️" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleCopyPress}>
            <SimpleIcon name="copy" size={20} color="#A0A0A0" fallbackText="📋" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleCreateNotePress}>
            <SimpleIcon name="add" size={20} color="#A0A0A0" fallbackText="+" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Clipboard History */}
      <Text style={styles.historyTitle}>Clipboard History</Text>
      <FlatList
        style={styles.historyList}
        data={clipboardItems}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <Suspense fallback={<View style={styles.loadingItem}><ActivityIndicator color="#A0A0A0" /></View>}>
            <ClipboardItem item={item} onPress={handleItemPress} />
          </Suspense>
        )}
      />


    </SafeAreaView>
  );
}

// Add styles for loading states
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Original Clipsy mobile design from demo.html
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 8,
  },
  connected: {
    backgroundColor: '#4CAF50',
    shadowColor: '#4CAF50',
  },
  disconnected: {
    backgroundColor: '#F44336',
    shadowColor: '#F44336',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  deviceCount: {
    fontSize: 14,
    color: '#A0A0A0',
    fontWeight: 'normal',
  },
  iconButton: {
    padding: 8,
  },
  iconText: {
    fontSize: 20,
  },
  livePreview: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    margin: 16,
  },
  liveText: {
    color: '#E0E0E0',
    fontSize: 16,
    marginBottom: 12,
  },
  actionIcons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionIcon: {
    fontSize: 20,
    marginLeft: 12,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#A0A0A0',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  historyList: {
    flex: 1,
    marginHorizontal: 16,
  },
  historyItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  timestamp: {
    fontSize: 12,
    color: '#A0A0A0',
    marginBottom: 4,
  },
  itemContent: {
    color: '#E0E0E0',
    fontSize: 14,
  },
  expandedContent: {
    maxHeight: undefined,
  },




  testScreenHeader: {
    backgroundColor: '#201c1c', // Matching Windows desktop HEX #201c1c
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  closeButton: {
    padding: 8,
  },

  loadingItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#201c1c', // Matching Windows desktop HEX #201c1c
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#1E1E1E',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  settingText: {
    color: '#E0E0E0',
    fontSize: 16,
  },
  editTextInput: {
    backgroundColor: '#1E1E1E',
    color: '#E0E0E0',
    padding: 16,
    borderRadius: 12,
    fontSize: 16,
    minHeight: 200,
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    backgroundColor: '#2D3748',
    padding: 12,
    borderRadius: 20,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 20,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#E0E0E0',
    fontWeight: '500',
    fontSize: 16,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    fontSize: 16,
  },
});