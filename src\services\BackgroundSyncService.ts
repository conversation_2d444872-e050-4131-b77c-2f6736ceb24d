/**
 * Background Sync Service for Clipsy Android App
 * Handles background clipboard synchronization and monitoring
 */

import { Platform } from 'react-native';
import WebSocketService from './WebSocketService';

// Platform-specific imports
let TaskManager: any = null;
let BackgroundFetch: any = null;
let Clipboard: any = null;
let AsyncStorage: any = null;

if (Platform.OS !== 'web') {
  try {
    TaskManager = require('expo-task-manager');
    BackgroundFetch = require('expo-background-fetch');
    Clipboard = require('@react-native-clipboard/clipboard').default;
    AsyncStorage = require('@react-native-async-storage/async-storage').default;
  } catch (error) {
    console.warn('Native modules not available on this platform:', error);
  }
}

const BACKGROUND_SYNC_TASK = 'CLIPSY_BACKGROUND_SYNC';
const CLIPBOARD_CHECK_INTERVAL = 5000; // 5 seconds
const LAST_CLIPBOARD_KEY = 'last_clipboard_content';
const SYNC_ENABLED_KEY = 'background_sync_enabled';

export interface BackgroundSyncCallbacks {
  onClipboardChange: (content: string) => void;
  onSyncComplete: () => void;
  onSyncError: (error: string) => void;
}

class BackgroundSyncService {
  private callbacks: BackgroundSyncCallbacks | null = null;
  private isRegistered = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private lastClipboardContent = '';
  private isEnabled = true;

  /**
   * Initialize background sync service
   */
  async initialize(callbacks: BackgroundSyncCallbacks) {
    this.callbacks = callbacks;

    // Check if we're in a development environment (Expo Go)
    const isDevelopment = !AsyncStorage || !TaskManager || !BackgroundFetch;

    if (Platform.OS === 'web') {
      console.log('📱 Background sync: Web platform detected, using fallback mode');
      return;
    }

    if (isDevelopment) {
      console.log('📱 Background sync running in development mode (limited functionality)');
      // In development mode, we'll use simplified sync without native modules
      this.isEnabled = true;
      return;
    }

    // Load last clipboard content (production mode)
    try {
      const lastContent = await AsyncStorage.getItem(LAST_CLIPBOARD_KEY);
      if (lastContent) {
        this.lastClipboardContent = lastContent;
      }
    } catch (error) {
      console.error('Failed to load last clipboard content:', error);
    }

    // Load sync enabled state (production mode)
    try {
      const syncEnabled = await AsyncStorage.getItem(SYNC_ENABLED_KEY);
      this.isEnabled = syncEnabled !== 'false';
    } catch (error) {
      console.error('Failed to load sync enabled state:', error);
    }

    // Register background task (production mode)
    await this.registerBackgroundTask();
  }

  /**
   * Register background fetch task
   */
  private async registerBackgroundTask() {
    if (Platform.OS === 'web') {
      console.log('📱 Background tasks: Web platform detected, using fallback mode');
      return;
    }

    if (!TaskManager || !BackgroundFetch) {
      console.log('📱 Background tasks running in development mode (native modules not available)');
      return;
    }

    try {
      // Define the background task
      TaskManager.defineTask(BACKGROUND_SYNC_TASK, async () => {
        console.log('Background sync task running...');
        
        try {
          if (!this.isEnabled) {
            console.log('Background sync disabled, skipping...');
            return BackgroundFetch.BackgroundFetchResult.NoData;
          }

          // Check clipboard for changes
          const currentClipboard = await Clipboard.getString();
          
          if (currentClipboard && currentClipboard !== this.lastClipboardContent) {
            console.log('Clipboard changed in background:', currentClipboard.substring(0, 50) + '...');
            
            // Update last clipboard content
            this.lastClipboardContent = currentClipboard;
            await AsyncStorage.setItem(LAST_CLIPBOARD_KEY, currentClipboard);
            
            // Sync with connected devices if WebSocket is connected
            if (WebSocketService.getConnectionStatus() === 'connected') {
              WebSocketService.sendClipboard(currentClipboard);
              console.log('Clipboard synced to connected devices');
            }
            
            // Notify callbacks
            this.callbacks?.onClipboardChange(currentClipboard);
            this.callbacks?.onSyncComplete();
            
            return BackgroundFetch.BackgroundFetchResult.NewData;
          }
          
          return BackgroundFetch.BackgroundFetchResult.NoData;
        } catch (error) {
          console.error('Background sync error:', error);
          this.callbacks?.onSyncError(error.toString());
          return BackgroundFetch.BackgroundFetchResult.Failed;
        }
      });

      // Register background fetch
      const status = await BackgroundFetch.getStatusAsync();
      if (status === BackgroundFetch.BackgroundFetchStatus.Available) {
        await BackgroundFetch.registerTaskAsync(BACKGROUND_SYNC_TASK, {
          minimumInterval: 15000, // 15 seconds minimum interval
          stopOnTerminate: false,
          startOnBoot: true,
        });
        
        this.isRegistered = true;
        console.log('Background sync task registered successfully');
      } else {
        console.warn('Background fetch not available:', status);
      }
    } catch (error) {
      console.error('Failed to register background task:', error);
    }
  }

  /**
   * Start foreground clipboard monitoring
   */
  startForegroundSync() {
    if (Platform.OS === 'web') {
      console.log('📱 Foreground sync: Web platform detected, using fallback mode');
      return;
    }

    if (!Clipboard) {
      console.log('📱 Foreground sync running in development mode (clipboard module not available)');
      // In development mode, we'll simulate sync functionality
      this.startDevelopmentSync();
      return;
    }

    if (this.syncInterval) {
      this.stopForegroundSync();
    }

    this.syncInterval = setInterval(async () => {
      try {
        if (!this.isEnabled) return;

        // Check if we're in a browser environment and handle clipboard access carefully
        let currentClipboard = '';
        try {
          currentClipboard = await Clipboard.getString();
        } catch (clipboardError) {
          // Browser clipboard access requires user interaction or document focus
          if (clipboardError.name === 'NotAllowedError' ||
              clipboardError.message.includes('not focused') ||
              clipboardError.message.includes('Document is not focused') ||
              clipboardError.message.includes('readText')) {
            // Silently skip this check - browser requires user interaction or focus
            console.debug('Clipboard access skipped - document not focused or user interaction required');
            return;
          }
          throw clipboardError; // Re-throw other errors
        }

        if (currentClipboard && currentClipboard !== this.lastClipboardContent) {
          console.log('Clipboard changed in foreground:', currentClipboard.substring(0, 50) + '...');

          this.lastClipboardContent = currentClipboard;
          await AsyncStorage.setItem(LAST_CLIPBOARD_KEY, currentClipboard);

          // Sync with connected devices
          if (WebSocketService.getConnectionStatus() === 'connected') {
            WebSocketService.sendClipboard(currentClipboard);
          }

          this.callbacks?.onClipboardChange(currentClipboard);
          this.callbacks?.onSyncComplete();
        }
      } catch (error) {
        // Only log non-clipboard access errors
        if (!error.message.includes('not focused') &&
            !error.message.includes('NotAllowedError') &&
            !error.message.includes('Document is not focused') &&
            !error.message.includes('readText') &&
            error.name !== 'NotAllowedError') {
          console.error('Foreground sync error:', error);
          this.callbacks?.onSyncError(error.toString());
        } else {
          // Silently handle clipboard access errors
          console.debug('Sync skipped - clipboard access requires focus/interaction');
        }
      }
    }, CLIPBOARD_CHECK_INTERVAL);

    console.log('Foreground clipboard monitoring started');
  }

  /**
   * Stop foreground clipboard monitoring
   */
  stopForegroundSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('Foreground clipboard monitoring stopped');
    }
  }

  /**
   * Enable or disable background sync
   */
  async setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    
    try {
      await AsyncStorage.setItem(SYNC_ENABLED_KEY, enabled.toString());
      
      if (enabled) {
        console.log('Background sync enabled');
        this.startForegroundSync();
      } else {
        console.log('Background sync disabled');
        this.stopForegroundSync();
      }
    } catch (error) {
      console.error('Failed to save sync enabled state:', error);
    }
  }

  /**
   * Check if background sync is enabled
   */
  isBackgroundSyncEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Get background fetch status
   */
  async getBackgroundFetchStatus(): Promise<string> {
    try {
      const status = await BackgroundFetch.getStatusAsync();
      
      switch (status) {
        case BackgroundFetch.BackgroundFetchStatus.Available:
          return 'Available';
        case BackgroundFetch.BackgroundFetchStatus.Denied:
          return 'Denied';
        case BackgroundFetch.BackgroundFetchStatus.Restricted:
          return 'Restricted';
        default:
          return 'Unknown';
      }
    } catch (error) {
      console.error('Failed to get background fetch status:', error);
      return 'Error';
    }
  }

  /**
   * Force sync current clipboard content
   */
  async forceSyncClipboard() {
    try {
      const currentClipboard = await Clipboard.getString();
      
      if (currentClipboard) {
        this.lastClipboardContent = currentClipboard;
        await AsyncStorage.setItem(LAST_CLIPBOARD_KEY, currentClipboard);
        
        if (WebSocketService.getConnectionStatus() === 'connected') {
          WebSocketService.sendClipboard(currentClipboard);
          console.log('Clipboard force synced to connected devices');
          this.callbacks?.onSyncComplete();
        } else {
          this.callbacks?.onSyncError('No connected devices to sync with');
        }
      }
    } catch (error) {
      console.error('Failed to force sync clipboard:', error);
      this.callbacks?.onSyncError(error.toString());
    }
  }

  /**
   * Update clipboard content from remote device
   */
  async updateClipboardFromRemote(content: string) {
    try {
      // Update local clipboard
      await Clipboard.setString(content);
      
      // Update last content to prevent sync loop
      this.lastClipboardContent = content;
      await AsyncStorage.setItem(LAST_CLIPBOARD_KEY, content);
      
      console.log('Clipboard updated from remote device');
      this.callbacks?.onClipboardChange(content);
    } catch (error) {
      console.error('Failed to update clipboard from remote:', error);
      this.callbacks?.onSyncError(error.toString());
    }
  }

  /**
   * Get last clipboard content
   */
  getLastClipboardContent(): string {
    return this.lastClipboardContent;
  }

  /**
   * Start development mode sync (simplified)
   */
  private startDevelopmentSync() {
    console.log('📱 Starting development mode sync simulation');

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Simulate sync activity every 10 seconds in development
    this.syncInterval = setInterval(() => {
      if (!this.isEnabled) return;

      console.log('📱 Development sync check (simulated)');

      // Simulate occasional sync events for testing
      if (Math.random() < 0.1) { // 10% chance
        const simulatedContent = `Development sync test - ${new Date().toLocaleTimeString()}`;
        this.callbacks.onClipboardChange?.(simulatedContent);
        console.log('📱 Simulated clipboard change:', simulatedContent);
      }
    }, 10000); // Check every 10 seconds in development
  }

  /**
   * Cleanup background sync service
   */
  async cleanup() {
    this.stopForegroundSync();

    if (this.isRegistered && BackgroundFetch) {
      try {
        await BackgroundFetch.unregisterTaskAsync(BACKGROUND_SYNC_TASK);
        this.isRegistered = false;
        console.log('Background sync task unregistered');
      } catch (error) {
        console.error('Failed to unregister background task:', error);
      }
    }
  }

  /**
   * Get sync statistics
   */
  async getSyncStats() {
    try {
      const lastContent = await AsyncStorage.getItem(LAST_CLIPBOARD_KEY);
      const syncEnabled = await AsyncStorage.getItem(SYNC_ENABLED_KEY);
      const backgroundStatus = await this.getBackgroundFetchStatus();
      
      return {
        lastClipboardLength: lastContent ? lastContent.length : 0,
        syncEnabled: syncEnabled !== 'false',
        backgroundFetchStatus: backgroundStatus,
        isRegistered: this.isRegistered,
        foregroundSyncActive: this.syncInterval !== null
      };
    } catch (error) {
      console.error('Failed to get sync stats:', error);
      return null;
    }
  }
}

// Export singleton instance
export const backgroundSyncService = new BackgroundSyncService();
export default BackgroundSyncService;
