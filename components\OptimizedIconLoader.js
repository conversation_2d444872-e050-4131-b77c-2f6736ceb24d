import React, { Suspense } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { dynamicImport } from '../utils/dynamicImport';

// Dynamically import the OptimizedIcon component
const LazyOptimizedIcon = dynamicImport(() => import('./OptimizedIcon'));

/**
 * A wrapper component that lazy loads the OptimizedIcon component
 * This helps reduce the initial bundle size
 */
const OptimizedIconLoader = (props) => {
  return (
    <Suspense fallback={
      <View style={{ width: props.size, height: props.size, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="small" color={props.color || '#999'} />
      </View>
    }>
      <LazyOptimizedIcon {...props} />
    </Suspense>
  );
};

export default OptimizedIconLoader;
