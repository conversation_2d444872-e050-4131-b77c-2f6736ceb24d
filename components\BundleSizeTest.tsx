import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import OptimizedIcon from './OptimizedIcon';

/**
 * BundleSizeTest Component
 * 
 * This component is used to test the bundle size optimization
 * by loading multiple icons using the OptimizedIcon component.
 */
const BundleSizeTest: React.FC = () => {
  const [loadedIcons, setLoadedIcons] = useState<number>(0);
  const icons = [
    'settings',
    'content-copy',
    'delete',
    'edit',
    'sync',
    'note-add',
    'home',
    'menu',
    'search',
    'person'
  ];

  useEffect(() => {
    // Simulate loading icons one by one to demonstrate
    // the lazy loading and code splitting functionality
    let count = 0;
    const interval = setInterval(() => {
      if (count < icons.length) {
        count++;
        setLoadedIcons(count);
      } else {
        clearInterval(interval);
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bundle Size Optimization Test</Text>
      <Text style={styles.subtitle}>
        Loaded {loadedIcons} of {icons.length} icons
      </Text>
      
      <View style={styles.iconGrid}>
        {icons.slice(0, loadedIcons).map((iconName, index) => (
          <View key={index} style={styles.iconContainer}>
            <OptimizedIcon 
              name={iconName} 
              size={24} 
              color="#E0E0E0" 
            />
            <Text style={styles.iconLabel}>{iconName}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#A0A0A0',
    marginBottom: 16,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconContainer: {
    alignItems: 'center',
    width: '30%',
    marginBottom: 16,
  },
  iconLabel: {
    fontSize: 12,
    color: '#A0A0A0',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default BundleSizeTest;
