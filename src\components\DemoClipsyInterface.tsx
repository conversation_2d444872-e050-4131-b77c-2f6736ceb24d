import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  SafeAreaView,
  TextInput,
  Switch,
  Image,
  Platform
} from 'react-native';
// Platform-aware QR Code import
let QRCode: any = null;
try {
  QRCode = require('react-native-qrcode-svg').default;
  console.log('✅ QRCode library loaded successfully');
} catch (error) {
  console.warn('⚠️ QRCode library not available:', error);
}

// Platform-aware imports with better error handling
let Clipboard: any = null;
let QRCodeScanner: any = null;
let BarCodeScanner: any = null;

// Initialize clipboard functionality for all platforms
const initializeClipboard = () => {
  if (Platform.OS !== 'web') {
    try {
      Clipboard = require('@react-native-clipboard/clipboard').default;
      console.log('Native clipboard module loaded successfully');
    } catch (error) {
      console.warn('Native clipboard not available, using fallback:', error);
      // Fallback for React Native when native module fails
      Clipboard = {
        getString: async () => {
          console.warn('Native clipboard getString not available');
          return '';
        },
        setString: async (text: string) => {
          console.warn('Native clipboard setString not available');
          throw new Error('Clipboard not available on this device');
        }
      };
    }
  } else {
    // Web clipboard implementation
    Clipboard = {
      getString: async () => {
        try {
          if (navigator.clipboard && navigator.clipboard.readText) {
            return await navigator.clipboard.readText();
          } else {
            console.warn('Web clipboard read not available');
            return '';
          }
        } catch (error) {
          console.warn('Clipboard read access denied:', error);
          return '';
        }
      },
      setString: async (text: string) => {
        try {
          if (navigator.clipboard && navigator.clipboard.writeText) {
            await navigator.clipboard.writeText(text);
            console.log('Text copied to web clipboard successfully');
          } else {
            throw new Error('Web clipboard not supported');
          }
        } catch (error) {
          console.error('Web clipboard write failed:', error);
          throw error;
        }
      }
    };
  }
};

// Initialize QR and camera modules for non-web platforms
if (Platform.OS !== 'web') {
  try {
    QRCodeScanner = require('./QRCodeScanner').default;
    const ExpoCamera = require('expo-camera');
    console.log('QR modules loaded successfully');
  } catch (error) {
    console.warn('QR modules not available:', error);
  }
}

// Initialize clipboard on module load
initializeClipboard();
import WebSocketService from '../services/WebSocketService';
import { QRCodeData } from '../services/QRCodeService';
import { backgroundSyncService } from '../services/BackgroundSyncService';
import { networkDiscoveryService, DiscoveredDevice } from '../services/NetworkDiscoveryService';
import { permissionsService, AllPermissions } from '../services/PermissionsService';
import { deviceInfoService, DeviceInformation } from '../services/DeviceInfoService';
import { floatingOverlayService } from '../services/FloatingOverlayService';
import FloatingClipboardWidget from './FloatingClipboardWidget';
import clipboardService from '../../services/ClipboardService';
// Temporarily commented out to fix import errors
// import { syncStatusManager } from '../services/SyncStatusManager';
// import { androidToAndroidSync } from '../services/AndroidToAndroidSync';
// import { windowsToWindowsSync } from '../services/WindowsToWindowsSync';
// import { androidToWindowsSync } from '../services/AndroidToWindowsSync';

interface ClipsyInterfaceProps {
  onClipboardUpdate?: (content: string) => void;
}

interface HistoryItem {
  id: string;
  content: string;
  timestamp: string;
}

interface Device {
  id: string;
  name: string;
  type: string;
  status: 'connected' | 'disconnected' | 'discovering';
  lastSeen: string;
  ipAddress: string;
}

interface SyncSettings {
  autoSync: boolean;
  syncDelay: number;
  syncOnConnect: boolean;
  bidirectional: boolean;
}







const DemoClipsyInterface: React.FC<ClipsyInterfaceProps> = ({ onClipboardUpdate }) => {
  // NEW DEVICE CLIPBOARD STATES - MATCH SCREENSHOT CONTENT
  const [thisDeviceClipboard, setThisDeviceClipboard] = useState<string>('Welcome to Clipsy! This is your Android device clipboard content. You can edit this content and it will sync with connected devices.');
  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState<string>('This is the clipboard content from your connected Desktop PC. You can edit this content and it will be sent to the connected device.');
  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);
  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);
  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');
  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');

  // REMOVED LIVE PREVIEW - KEEPING ONLY DEVICE SECTIONS
  
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([
    { id: '1', content: 'This is an older clipboard item. It\'s shorter.', timestamp: '2 minutes ago' },
    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },
    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },
    { id: '4', content: 'Some code snippet: function hello() { console.log("Hello World!"); }', timestamp: '5 hours ago' }
  ]);
  
  // UI State
  const [showSettings, setShowSettings] = useState(false);
  const [showSyncSettings, setShowSyncSettings] = useState(false);
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // QR Code Scanner State
  const [isQRScannerVisible, setIsQRScannerVisible] = useState(false);
  const [isQRGeneratorVisible, setIsQRGeneratorVisible] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'pairing' | 'error'>('disconnected');

  // Floating Overlay State
  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);

  // Background Sync State
  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);
  const [networkDevices, setNetworkDevices] = useState<DiscoveredDevice[]>([]);
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [isDiscoverable, setIsDiscoverable] = useState(true);

  // Permissions and Device Info State
  const [permissions, setPermissions] = useState<AllPermissions | null>(null);
  const [deviceInformation, setDeviceInformation] = useState<DeviceInformation | null>(null);
  const [permissionsChecked, setPermissionsChecked] = useState(false);
  
  // Sync Settings - EXACTLY FROM DEMO.HTML
  const [syncSettings, setSyncSettings] = useState<SyncSettings>({
    autoSync: true,
    syncDelay: 2,
    syncOnConnect: true,
    bidirectional: true
  });

  // Device Management - EXACTLY FROM DEMO.HTML
  const [pairedDevices, setPairedDevices] = useState<Device[]>([
    {
      id: 'desktop-1',
      name: 'Desktop PC - Office',
      type: 'Windows 11',
      status: 'connected',
      lastSeen: '2 min ago',
      ipAddress: '*************'
    },
    {
      id: 'linux-1',
      name: 'Ubuntu Server - Home',
      type: 'Ubuntu 22.04',
      status: 'disconnected',
      lastSeen: '1 hour ago',
      ipAddress: '*************'
    }
  ]);

  const [discoveredDevices] = useState<Device[]>([
    {
      id: 'johns-laptop',
      name: 'John\'s Laptop',
      type: 'Windows 10',
      status: 'discovering',
      lastSeen: 'Available for pairing',
      ipAddress: '*************'
    },
    {
      id: 'sarahs-desktop',
      name: 'Sarah\'s Desktop',
      type: 'Ubuntu 22.04',
      status: 'discovering',
      lastSeen: 'Available for pairing',
      ipAddress: '*************'
    }
  ]);

  // Device Info - Dynamic based on actual device information
  const getDeviceInfo = () => {
    const deviceName = deviceInformation?.name ||
                      deviceInformation?.model ||
                      `${Platform.OS === 'android' ? 'Android' : Platform.OS === 'ios' ? 'iOS' : 'Web'} Device`;

    const ipAddress = deviceInformation?.networkInfo?.ipAddress || 'Unknown';

    // Check if any paired device is connected
    const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');

    const status = hasConnectedDevices
      ? 'Connected'
      : pairedDevices.length > 0
        ? 'Disconnected'
        : 'Not Connected';

    return {
      deviceName,
      ipAddress,
      status
    };
  };

  // FUNCTIONS - EXACTLY FROM DEMO.HTML
  const showMessage = (text: string) => {
    setSuccessMessage(text);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  // Monitor paired devices and update connection status
  useEffect(() => {
    const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
    const newConnectionStatus = hasConnectedDevices ? 'connected' : 'disconnected';

    if (connectionStatus !== newConnectionStatus) {
      setConnectionStatus(newConnectionStatus);
      console.log('Connection status updated based on paired devices:', newConnectionStatus);
    }
  }, [pairedDevices, connectionStatus]);

  // INITIALIZE SERVICES (NON-BLOCKING)
  useEffect(() => {
    const initializeServices = async () => {
      try {
        console.log('DemoClipsyInterface: Starting service initialization');

        // Initialize services in background without blocking UI
        setTimeout(async () => {
          try {
            // Initialize device info service first
            const deviceInfo = await deviceInfoService.initialize();
            setDeviceInformation(deviceInfo);
            console.log('Device info initialized:', deviceInfo);
          } catch (error) {
            console.warn('Device info initialization failed:', error);
          }
        }, 100);

        setTimeout(async () => {
          try {
            // Initialize permissions service
            permissionsService.initialize({
              onPermissionGranted: (permission: string) => {
                console.log(`Permission granted: ${permission}`);
                showMessage(`${permission} permission granted`);
              },
              onPermissionDenied: (permission: string) => {
                console.log(`Permission denied: ${permission}`);
                showMessage(`${permission} permission denied`);
              },
              onAllPermissionsGranted: () => {
                console.log('All permissions granted');
                showMessage('All permissions granted!');
              },
              onCriticalPermissionDenied: (permission: string) => {
                console.log(`Critical permission denied: ${permission}`);
                showMessage(`Critical permission denied: ${permission}`);
              }
            });

            // Request all permissions
            const permissionResults = await permissionsService.requestAllPermissions();
            setPermissions(permissionResults);
            setPermissionsChecked(true);
            console.log('Permissions checked:', permissionResults);
          } catch (error) {
            console.warn('Permissions initialization failed:', error);
          }
        }, 500);

        setTimeout(async () => {
          try {
            // Initialize background sync service
            backgroundSyncService.initialize({
              onClipboardChange: (content: string) => {
                setThisDeviceClipboard(content);
                showMessage('Clipboard synced from background');
              },
              onSyncComplete: () => {
                console.log('Background sync completed');
              },
              onSyncError: (error: string) => {
                console.error('Background sync error:', error);
                showMessage(`Sync error: ${error}`);
              }
            });

            // Start background sync
            backgroundSyncService.startForegroundSync();

            // Initialize floating overlay service
            floatingOverlayService.initialize({
              onClipboardCopy: (content: string) => {
                showMessage(`📋 Copied: ${content.substring(0, 30)}...`);
              },
              onOverlayToggle: (visible: boolean) => {
                setIsFloatingOverlayVisible(visible);
                showMessage(visible ? '🔄 Floating widget shown' : '🔄 Floating widget hidden');
              },
              onError: (error: string) => {
                console.error('Floating overlay error:', error);
                showMessage(`❌ Overlay error: ${error}`);
              }
            });

            // Initialize cross-platform sync services (temporarily disabled)
            // syncStatusManager.initialize({
            //   onStatusUpdate: (deviceId, status) => {
            //     console.log(`Device ${deviceId} status updated:`, status.status);
            //   },
            //   onConflictDetected: (conflict) => {
            //     showMessage(`⚠️ Sync conflict detected between ${conflict.sourceDevice} and ${conflict.targetDevice}`);
            //   },
            //   onConflictResolved: (conflict) => {
            //     showMessage(`✅ Sync conflict resolved using ${conflict.resolutionMethod}`);
            //   },
            //   onSyncComplete: (deviceId, success) => {
            //     const device = discoveredDevices.find(d => d.id === deviceId);
            //     if (device) {
            //       showMessage(success ?
            //         `✅ Synced with ${device.name}` :
            //         `❌ Sync failed with ${device.name}`
            //       );
            //     }
            //   },
            //   onOverallStatsUpdate: (stats) => {
            //     console.log('Overall sync stats:', stats);
            //   }
            // });

            // Initialize clipboard content using improved service
            try {
              const currentClipboard = await clipboardService.getClipboardText();
              if (currentClipboard && currentClipboard.trim() &&
                  currentClipboard !== 'Welcome to Clipsy! This is your Android device clipboard content. You can edit this content and it will sync with connected devices.' &&
                  currentClipboard !== 'Demo clipboard content - Welcome to Clipsy!') {
                setThisDeviceClipboard(currentClipboard);
                console.log('📋 Initial clipboard loaded:', currentClipboard.substring(0, 50) + '...');
                showMessage('📋 Clipboard content detected');
              }
            } catch (error) {
              console.warn('⚠️ Failed to get initial clipboard content:', error);
            }
          } catch (error) {
            console.warn('Background sync initialization failed:', error);
          }
        }, 1000);

        setTimeout(async () => {
          try {
            // Initialize network discovery service
            networkDiscoveryService.initialize({
              onDeviceFound: (device: DiscoveredDevice) => {
                setNetworkDevices(prev => {
                  const exists = prev.find(d => d.id === device.id);
                  if (!exists) {
                    showMessage(`Device found: ${device.name}`);
                    return [...prev, device];
                  }
                  return prev;
                });
              },
              onDeviceLost: (deviceId: string) => {
                setNetworkDevices(prev => prev.filter(d => d.id !== deviceId));
              },
              onNetworkChanged: (networkInfo) => {
                console.log('Network changed:', networkInfo);
              },
              onDiscoveryComplete: (devices: DiscoveredDevice[]) => {
                setNetworkDevices(devices);
              },
              onError: (error: string) => {
                console.error('Network discovery error:', error);
              }
            });

            // Network discovery service is initialized but not started automatically
            // User can start discovery manually using the Discover button
          } catch (error) {
            console.warn('Network discovery initialization failed:', error);
          }
        }, 1500);

      } catch (error) {
        console.error('Failed to initialize services:', error);
        // Don't show error message to user, just log it
      }
    };

    // Initialize services
    initializeServices();

    // Set up periodic clipboard monitoring using improved service
    const clipboardInterval = setInterval(async () => {
      try {
        const currentClipboard = await clipboardService.getClipboardText();
        if (currentClipboard &&
            currentClipboard.trim() &&
            currentClipboard !== thisDeviceClipboard &&
            currentClipboard !== 'Demo clipboard content - Welcome to Clipsy!' &&
            currentClipboard.length > 0) {
          setThisDeviceClipboard(currentClipboard);
          console.log('📋 Clipboard updated externally:', currentClipboard.substring(0, 50) + '...');
          showMessage('📋 Clipboard updated');
        }
      } catch (error) {
        // Silently ignore clipboard access errors
        console.debug('📋 Clipboard monitoring error:', error);
      }
    }, 3000); // Check every 3 seconds

    // Cleanup on unmount
    return () => {
      try {
        backgroundSyncService.stopForegroundSync();
        networkDiscoveryService.stopDiscovery();
        clearInterval(clipboardInterval);
      } catch (error) {
        console.warn('Error during cleanup:', error);
      }
    };
  }, [thisDeviceClipboard]);

  // REMOVED EDIT TEXT - USING INDIVIDUAL DEVICE EDIT FUNCTIONS

  // REMOVED COPY TEXT AND CREATE NOTE - USING INDIVIDUAL DEVICE FUNCTIONS

  const selectItem = async (item: HistoryItem) => {
    try {
      // Copy to device clipboard
      await copyToClipboard(item.content);

      // Update connected device clipboard when selecting from history
      setConnectedDeviceClipboard(item.content);

      // Provide visual feedback
      showMessage('📋 Copied to clipboard!');
      onClipboardUpdate?.(item.content);
    } catch (error) {
      console.error('Failed to copy history item:', error);
      showMessage('❌ Failed to copy item');
    }
  };

  const syncNow = async () => {
    try {
      // First, refresh clipboard content using improved service
      try {
        const currentClipboard = await clipboardService.getClipboardText();
        if (currentClipboard && currentClipboard.trim() &&
            currentClipboard !== thisDeviceClipboard &&
            currentClipboard !== 'Demo clipboard content - Welcome to Clipsy!') {
          setThisDeviceClipboard(currentClipboard);
          showMessage('📋 Clipboard content refreshed');
        }
      } catch (error) {
        console.warn('⚠️ Failed to refresh clipboard during sync:', error);
      }

      // Update device connection status based on paired devices
      const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
      setConnectionStatus(hasConnectedDevices ? 'connected' : 'disconnected');

      // Perform sync if auto sync is enabled
      if (!syncSettings.autoSync) {
        showMessage('🔄 Manual refresh completed. Enable auto sync for device synchronization.');
        return;
      }

      if (syncSettings.syncDelay === 0) {
        showMessage('⚡ Instant sync: Syncing with paired devices...');
        setTimeout(() => {
          showMessage('✅ Instant sync completed!');
        }, 500);
      } else {
        showMessage(`⏱️ Sync scheduled in ${syncSettings.syncDelay} seconds...`);
        setTimeout(() => {
          showMessage('🔄 Syncing with paired devices...');
          setTimeout(() => {
            showMessage('✅ Sync completed!');
          }, 1000);
        }, syncSettings.syncDelay * 1000);
      }
    } catch (error) {
      console.error('Sync error:', error);
      showMessage('❌ Sync failed. Please try again.');
    }
  };

  const testClipboard = () => {
    showMessage('Opening clipboard test screen...');
  };

  const toggleAlwaysOnTop = () => {
    setIsAlwaysOnTop(!isAlwaysOnTop);
    if (!isAlwaysOnTop) {
      showMessage('📌 App pinned to top');
    } else {
      showMessage('📌 App unpinned from top');
    }
  };

  const minimizeToTray = () => {
    showMessage('➖ Minimizing to background...');
  };

  const toggleFloatingOverlay = async () => {
    try {
      await floatingOverlayService.toggleOverlay();

      // Update clipboard items with current connected device content
      const clipboardItems = [
        {
          id: '1',
          content: connectedDeviceClipboard,
          timestamp: 'Now',
          deviceName: 'Desktop PC',
          deviceType: 'windows' as const
        },
        {
          id: '2',
          content: thisDeviceClipboard,
          timestamp: 'Local',
          deviceName: 'This Android Device',
          deviceType: 'android' as const
        }
      ];

      floatingOverlayService.updateClipboardItems(clipboardItems);

    } catch (error) {
      console.error('Error toggling floating overlay:', error);
      showMessage('❌ Failed to toggle floating widget');
    }
  };

  // Device Management Functions - EXACTLY FROM DEMO.HTML
  const connectDevice = (deviceId: string) => {
    showMessage(`Connecting to device: ${deviceId}...`);

    // Update device status to connected
    setPairedDevices(prevDevices => {
      const updatedDevices = prevDevices.map(device =>
        device.id === deviceId
          ? { ...device, status: 'connected' as const, lastSeen: 'Just now' }
          : device
      );

      // Update global connection status if any device is now connected
      const hasConnectedDevices = updatedDevices.some(device => device.status === 'connected');
      if (hasConnectedDevices) {
        setConnectionStatus('connected');
      }

      return updatedDevices;
    });

    setTimeout(() => {
      showMessage('Device connected successfully!');
    }, 1000);
  };

  const disconnectDevice = (deviceId: string) => {
    showMessage(`Disconnecting from device: ${deviceId}...`);

    // Update device status to disconnected
    setPairedDevices(prevDevices => {
      const updatedDevices = prevDevices.map(device =>
        device.id === deviceId
          ? { ...device, status: 'disconnected' as const, lastSeen: 'Just now' }
          : device
      );

      // Update global connection status if no devices are connected
      const hasConnectedDevices = updatedDevices.some(device => device.status === 'connected');
      if (!hasConnectedDevices) {
        setConnectionStatus('disconnected');
      }

      return updatedDevices;
    });

    setTimeout(() => {
      showMessage('Device disconnected successfully');
    }, 1000);
  };

  const removeDevice = (deviceId: string) => {
    console.log('Remove device called:', deviceId);

    Alert.alert(
      'Remove Device',
      'Are you sure you want to remove this device from paired devices?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => console.log('Remove cancelled for device:', deviceId)
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            console.log('Remove confirmed for device:', deviceId);
            // Remove device from paired devices list
            setPairedDevices(prevDevices => {
              const filteredDevices = prevDevices.filter(device => device.id !== deviceId);
              console.log('Devices after removal:', filteredDevices);

              // Update global connection status if no devices remain connected
              const hasConnectedDevices = filteredDevices.some(device => device.status === 'connected');
              if (!hasConnectedDevices) {
                setConnectionStatus('disconnected');
              }

              return filteredDevices;
            });
            showMessage('Device removed from paired devices');
          }
        }
      ]
    );
  };

  const pairDevice = async (deviceId: string) => {
    try {
      // Find the device in discovered devices
      const discoveredDevice = networkDevices.find(d => d.id === deviceId);
      if (!discoveredDevice) {
        showMessage('❌ Device not found');
        return;
      }

      showMessage(`🔄 Initiating pairing with: ${discoveredDevice.name}...`);

      // Check if device is already paired
      const alreadyPaired = pairedDevices.find(d => d.ipAddress === discoveredDevice.ip);
      if (alreadyPaired) {
        showMessage('⚠️ Device is already paired');
        return;
      }

      // Simulate pairing handshake process
      setTimeout(async () => {
        try {
          // Create new paired device
          const newPairedDevice: Device = {
            id: `paired-${Date.now()}`,
            name: discoveredDevice.name,
            type: discoveredDevice.type,
            status: 'connected',
            lastSeen: 'Just now',
            ipAddress: discoveredDevice.ip
          };

          // Add to paired devices list
          setPairedDevices(prev => {
            const updatedDevices = [...prev, newPairedDevice];

            // Update connection status since we're adding a connected device
            setConnectionStatus('connected');

            return updatedDevices;
          });

          // Remove from discovered devices (since it's now paired)
          setNetworkDevices(prev => prev.filter(d => d.id !== deviceId));

          showMessage('✅ Pairing successful! Device added to paired devices.');
          console.log('Device paired successfully:', newPairedDevice);

        } catch (error) {
          console.error('Pairing error:', error);
          showMessage('❌ Pairing failed. Please try again.');
        }
      }, 2000); // Simulate handshake delay

    } catch (error) {
      console.error('Pairing initiation error:', error);
      showMessage('❌ Failed to initiate pairing');
    }
  };

  const scanQRCode = async () => {
    console.log('QR scan requested, checking platform and modules...');

    // Check if we're on web platform
    if (Platform.OS === 'web') {
      Alert.alert(
        'QR Scanner Not Available',
        'QR code scanning is not available on web browsers. Please use the mobile app to scan QR codes, or use the device discovery feature instead.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if we're running in Expo Go or development mode
    const isExpoGo = typeof __DEV__ !== 'undefined' && __DEV__;

    // Check if BarCodeScanner module is available
    if (!BarCodeScanner) {
      console.error('BarCodeScanner module not available');
      Alert.alert(
        'QR Scanner Not Available',
        isExpoGo
          ? 'QR scanning requires a development build or production app. Please use device discovery instead, or build the app with EAS Build.'
          : 'Camera module is not available. Please ensure you are running on a physical device with camera support.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if QRCodeScanner component is available
    if (!QRCodeScanner) {
      console.error('QRCodeScanner component not loaded');
      Alert.alert(
        'QR Scanner Error',
        'QR scanner component failed to load. This may be due to missing camera permissions or running in a simulator. Please try on a physical device.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check camera permissions before opening scanner
    try {
      console.log('Checking camera permissions...');

      // First check if the device has camera capability
      const hasCamera = await BarCodeScanner.isAvailableAsync?.() ?? true;
      if (!hasCamera) {
        Alert.alert(
          'Camera Not Available',
          'This device does not have camera support required for QR scanning.',
          [{ text: 'OK' }]
        );
        return;
      }

      const { status } = await BarCodeScanner.getPermissionsAsync();
      console.log('Current camera permission status:', status);

      if (status !== 'granted') {
        console.log('Camera permission not granted, requesting...');
        const { status: newStatus } = await BarCodeScanner.requestPermissionsAsync();
        console.log('Permission request result:', newStatus);

        if (newStatus !== 'granted') {
          Alert.alert(
            'Camera Permission Required',
            'Camera access is required to scan QR codes. Please enable camera permissions in your device settings and restart the app.',
            [
              { text: 'Cancel' },
              { text: 'Open Settings', onPress: () => {
                // Open device settings
                if (Platform.OS === 'android') {
                  const { Linking } = require('react-native');
                  Linking.openSettings();
                } else if (Platform.OS === 'ios') {
                  const { Linking } = require('react-native');
                  Linking.openURL('app-settings:');
                }
              }}
            ]
          );
          return;
        }
      }

      console.log('Camera permissions granted, opening scanner...');
      setIsQRScannerVisible(true);

    } catch (error) {
      console.error('Error checking camera permissions:', error);
      Alert.alert(
        'Permission Error',
        'Failed to check camera permissions. This may be due to running in a simulator or missing camera hardware. Please try on a physical device.',
        [{ text: 'OK' }]
      );
      return;
    }
  };

  const generateQRCodeData = () => {
    const deviceId = `android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const token = generateSecureToken();
    const deviceName = encodeURIComponent('Clipsy Android Device');
    const localIP = '*************'; // In production, this would be detected automatically
    const port = 8080;

    // Use Clipsy protocol format
    const qrUrl = `clipsy://connect?ip=${localIP}&port=${port}&token=${token}&device_id=${deviceId}&device_name=${deviceName}&version=1.0`;

    console.log('📋 Generated QR data:', qrUrl);
    return qrUrl;
  };

  const generateSecureToken = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return token;
  };

  const generateQRCode = () => {
    console.log('🔧 QR generation requested, checking platform and modules...');
    console.log('📱 Platform:', Platform.OS);
    console.log('📋 QRCode available:', !!QRCode);

    // Check if QR generation is available
    if (!QRCode) {
      console.warn('❌ QRCode library not available');
      Alert.alert(
        'QR Code Generation Not Available',
        'QR code generation library is not available. Please ensure react-native-qrcode-svg is properly installed.',
        [
          {
            text: 'Use Device Discovery',
            onPress: () => {
              // Switch to device discovery tab
              setActiveTab('discovery');
            }
          },
          { text: 'OK' }
        ]
      );
      return;
    }

    console.log('✅ Opening QR generator...');
    setIsQRGeneratorVisible(true);
  };

  const handleQRScanSuccess = (data: QRCodeData) => {
    console.log('QR scan successful:', data);
    showMessage(`Connecting to ${data.device_name}...`);
  };

  const handleQRConnectionSuccess = () => {
    setConnectionStatus('connected');
    showMessage('Device connected successfully!');
  };

  const refreshDiscovery = async () => {
    try {
      showMessage('🔍 Scanning for available devices...');
      setIsDiscovering(true);

      // Clear existing discovered devices
      setNetworkDevices([]);

      // Start network discovery
      await networkDiscoveryService.startDiscovery();

      // Add some mock devices for testing (remove this in production)
      setTimeout(() => {
        const mockDevices: DiscoveredDevice[] = [
          {
            id: 'mock-desktop-1',
            name: 'Desktop PC',
            type: 'windows',
            ip: '*************',
            port: 8080,
            lastSeen: 'Just now',
            services: ['clipsy'],
            capabilities: {
              hasCamera: false,
              supportsBackgroundTasks: true,
              supportsNotifications: true,
              supportsClipboard: true,
              supportsQRScanning: false,
              supportsNetworkDiscovery: true
            }
          },
          {
            id: 'mock-laptop-1',
            name: 'Linux Laptop',
            type: 'linux',
            ip: '*************',
            port: 8081,
            lastSeen: '2 minutes ago',
            services: ['clipsy'],
            capabilities: {
              hasCamera: false,
              supportsBackgroundTasks: true,
              supportsNotifications: true,
              supportsClipboard: true,
              supportsQRScanning: false,
              supportsNetworkDiscovery: true
            }
          }
        ];

        setNetworkDevices(mockDevices);
        showMessage(`✅ Discovery complete. Found ${mockDevices.length} device(s).`);
        setIsDiscovering(false);
      }, 3000); // Show results after 3 seconds

    } catch (error) {
      console.error('Discovery error:', error);
      showMessage('❌ Discovery failed. Please try again.');
      setIsDiscovering(false);
    }
  };

  // NEW DEVICE EDIT FUNCTIONS
  const startEditingThisDevice = () => {
    setEditingThisDeviceText(thisDeviceClipboard);
    setIsEditingThisDevice(true);
  };

  const saveThisDeviceEdit = async () => {
    try {
      const newContent = editingThisDeviceText.trim();
      if (!newContent) {
        showMessage('Content cannot be empty');
        return;
      }

      console.log('Saving clipboard content:', newContent);

      // Update state first
      setThisDeviceClipboard(newContent);

      // Try to update device clipboard using improved service
      try {
        await clipboardService.setClipboardText(newContent);
        console.log('✅ Clipboard updated successfully');
      } catch (clipboardError) {
        console.warn('⚠️ Failed to update device clipboard:', clipboardError);
        // Continue anyway - state is updated
      }

      setIsEditingThisDevice(false);
      setEditingThisDeviceText('');
      showMessage('✅ This Device clipboard updated!');
      onClipboardUpdate?.(newContent);

    } catch (error) {
      console.error('Error saving clipboard:', error);
      showMessage('❌ Failed to save changes: ' + (error as Error).message);
    }
  };

  const cancelThisDeviceEdit = () => {
    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
  };

  const startEditingConnectedDevice = () => {
    setEditingConnectedDeviceText(connectedDeviceClipboard);
    setIsEditingConnectedDevice(true);
  };

  const saveConnectedDeviceEdit = async () => {
    try {
      const newContent = editingConnectedDeviceText.trim();
      if (!newContent) {
        showMessage('Content cannot be empty');
        return;
      }

      setConnectedDeviceClipboard(newContent);
      setIsEditingConnectedDevice(false);
      setEditingConnectedDeviceText('');
      showMessage('✅ Connected Device clipboard updated!');
      onClipboardUpdate?.(newContent);

      // TODO: Send to connected device via WebSocket
      console.log('Broadcasting to connected device:', newContent);
    } catch (error) {
      showMessage('❌ Failed to save changes');
    }
  };

  const cancelConnectedDeviceEdit = () => {
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
  };

  const copyToClipboard = async (content: string) => {
    try {
      if (!content || content.trim() === '') {
        showMessage('❌ No content to copy');
        return;
      }

      console.log('📋 Attempting to copy to clipboard:', content.substring(0, 50) + '...');

      // Check clipboard availability status
      const clipboardStatus = clipboardService.getClipboardStatus();

      if (!clipboardStatus.available) {
        console.log('📋 Development Mode: Simulating clipboard copy');
        showMessage('📋 Development Mode: Text copied to memory cache (use production build for real clipboard)');
      }

      // Use the clipboard service for better error handling
      await clipboardService.setClipboardText(content);

      // Update "This Device" clipboard to reflect what's now in device memory
      setThisDeviceClipboard(content);

      // Add to clipboard history
      const newHistoryItem: HistoryItem = {
        id: Date.now().toString(),
        content: content,
        timestamp: 'Just now'
      };

      // Add to beginning of history (most recent first)
      setHistoryItems(prevHistory => [newHistoryItem, ...prevHistory]);

      // Show appropriate success message based on clipboard availability
      if (clipboardStatus.available) {
        showMessage('✅ Text copied to clipboard!');
      } else {
        showMessage('📋 Development Mode: Text stored in memory cache');
      }

    } catch (error) {
      console.error('❌ Failed to copy to clipboard:', error);

      // Provide more specific error messages
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('permission') || errorMessage.includes('denied')) {
        showMessage('❌ Clipboard permission denied - please allow clipboard access');
      } else if (errorMessage.includes('not focused') || errorMessage.includes('focus')) {
        showMessage('❌ App needs focus to access clipboard - click here first');
      } else if (errorMessage.includes('not available') || errorMessage.includes('production build')) {
        showMessage('📋 Development Mode: Clipboard simulated (use production build for full functionality)');
      } else {
        showMessage('❌ Failed to copy - try selecting and copying manually');
      }
    }
  };

  // DELETE HISTORY ITEM FUNCTION
  const deleteHistoryItem = (itemId: string) => {
    const updatedHistory = historyItems.filter(item => item.id !== itemId);
    setHistoryItems(updatedHistory);
    showMessage('🗑️ History item deleted!');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - EXACTLY FROM DEMO.HTML */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../../assets/images/clipsy-logo-no-bg.png')}
              style={styles.appLogo}
              resizeMode="contain"
            />
            <View style={[
              styles.connectionDot,
              { backgroundColor: pairedDevices.some(device => device.status === 'connected') ? '#4CAF50' : '#F44336' }
            ]} />
          </View>
          <Text style={styles.title}>Clipsy</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.iconButton, isFloatingOverlayVisible && styles.iconButtonActive]}
            onPress={toggleFloatingOverlay}
            accessibilityLabel="Toggle floating clipboard widget"
            accessibilityHint="Shows or hides Android floating widget with PC and server clipboard content"
          >
            <Text style={styles.iconButtonText}>📋</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.iconButton, isAlwaysOnTop && styles.iconButtonActive]}
            onPress={toggleAlwaysOnTop}
          >
            <View style={styles.pinIcon}>
              <View style={styles.pinHead} />
              <View style={styles.pinBody} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={minimizeToTray}>
            <Text style={styles.iconButtonText}>➖</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={() => setShowSettings(true)}>
            <Text style={styles.iconButtonText}>⚙️</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Success Message - EXACTLY FROM DEMO.HTML */}
      {successMessage ? (
        <View style={styles.successMessage}>
          <Text style={styles.successMessageText}>{successMessage}</Text>
        </View>
      ) : null}

      {/* MAIN SCROLLABLE CONTENT */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
      >
        {/* MY DEVICE SECTION - MATCH SCREENSHOT */}
        <View style={styles.deviceSection}>
          <View style={styles.deviceSectionHeader}>
            <View style={styles.deviceSectionTitleContainer}>
              <Text style={styles.deviceSectionTitle}>📱 This Device</Text>
              <Text style={styles.deviceSectionSubtitle}>{getDeviceInfo().deviceName}</Text>
            </View>

          </View>

          {isEditingThisDevice ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.editTextInput}
                value={editingThisDeviceText}
                onChangeText={setEditingThisDeviceText}
                placeholder="Edit this device clipboard content..."
                placeholderTextColor="#A0A0A0"
                multiline
                autoFocus
              />
              <View style={styles.editActions}>
                <TouchableOpacity style={styles.saveButtonThisDevice} onPress={saveThisDeviceEdit}>
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButtonThisDevice} onPress={cancelThisDeviceEdit}>
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.deviceClipboard}>

              <TouchableOpacity
                style={[styles.clipboardContent, styles.thisDeviceContent]}
                onPress={() => copyToClipboard(thisDeviceClipboard)}
              >
                <Text style={styles.clipboardText}>{thisDeviceClipboard}</Text>
                <Text style={styles.clipboardMeta}>Tap to copy • Real-time sync</Text>
                <TouchableOpacity style={styles.editButtonInside} onPress={startEditingThisDevice}>
                  <Text style={styles.editButtonIcon}>✎</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            </View>
          )}
        </View>

      {/* CONNECTED DEVICE SECTION - MATCH SCREENSHOT */}
      <View style={styles.deviceSection}>
        <View style={styles.deviceSectionHeader}>
          <Text style={styles.deviceSectionTitle}>🔗 Connected Device</Text>
          <Text style={styles.deviceSectionSubtitle}>Desktop PC - Office</Text>
        </View>

        {isEditingConnectedDevice ? (
          <View style={styles.editContainer}>
            <TextInput
              style={styles.editTextInput}
              value={editingConnectedDeviceText}
              onChangeText={setEditingConnectedDeviceText}
              placeholder="Edit connected device clipboard content..."
              placeholderTextColor="#A0A0A0"
              multiline
              autoFocus
            />
            <View style={styles.editActions}>
              <TouchableOpacity style={styles.saveButtonConnectedDevice} onPress={saveConnectedDeviceEdit}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.cancelButtonConnectedDevice} onPress={cancelConnectedDeviceEdit}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={styles.deviceClipboard}>

            <TouchableOpacity
              style={[styles.clipboardContent, styles.connectedDeviceContent]}
              onPress={() => copyToClipboard(connectedDeviceClipboard)}
            >
              <Text style={styles.clipboardText}>{connectedDeviceClipboard}</Text>
              <Text style={styles.clipboardMeta}>Tap to copy • Bidirectional sync</Text>
              <TouchableOpacity style={styles.editButtonInside} onPress={startEditingConnectedDevice}>
                <Text style={styles.editButtonIcon}>✎</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* History - EXACTLY FROM DEMO.HTML + WHITE DELETE BUTTONS */}
      <Text style={styles.historyTitle}>Clipboard History</Text>
      <ScrollView style={styles.historyList}>
        {historyItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.historyItem}
            onPress={() => selectItem(item)}
          >
            <View style={styles.historyItemHeader}>
              <Text style={styles.timestamp}>{item.timestamp}</Text>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={(e) => {
                  e.stopPropagation(); // Prevent triggering selectItem
                  deleteHistoryItem(item.id);
                }}
              >
                <Text style={styles.deleteButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.itemContent}>{item.content}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      </ScrollView>

      {/* Floating Sync Button */}
      <TouchableOpacity style={styles.floatingSyncButton} onPress={syncNow}>
        <Image
          source={require('../../assets/images/sync.png')}
          style={styles.syncIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      {/* Settings Sidebar Modal - EXACTLY FROM DEMO.HTML */}
      <Modal
        visible={showSettings}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.settingsSidebar}>
            <View style={styles.settingsHeader}>
              <Text style={styles.settingsTitle}>Settings</Text>
              <TouchableOpacity onPress={() => setShowSettings(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.settingsContent}>
              {/* Device Info - Dynamic based on actual device */}
              <View style={styles.settingsSection}>
                <Text style={styles.sectionTitle}>Device Info</Text>
                <View style={styles.deviceInfoCard}>
                  <Text style={styles.deviceName}>{getDeviceInfo().deviceName}</Text>
                  <Text style={styles.deviceDetail}>IP: {getDeviceInfo().ipAddress}</Text>
                  <View style={styles.deviceStatusRow}>
                    <View style={[
                      styles.deviceStatusIndicator,
                      getDeviceInfo().status === 'Connected' ? styles.connected : styles.disconnected
                    ]} />
                    <Text style={[
                      styles.deviceStatusText,
                      getDeviceInfo().status === 'Connected' ? styles.connectedText : styles.disconnectedText
                    ]}>
                      Status: {getDeviceInfo().status}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Paired Devices - ENHANCED FROM TEST-FIXES.HTML */}
              <View style={styles.settingsSection}>
                <Text style={styles.sectionTitle}>Paired Devices</Text>

                {pairedDevices.length === 0 ? (
                  <View style={styles.emptyDeviceList}>
                    <Text style={styles.emptyDeviceText}>No paired devices found</Text>
                    <Text style={styles.emptyDeviceSubtext}>Use QR code or device discovery to pair devices</Text>
                  </View>
                ) : (
                  pairedDevices.map((device) => (
                    <View key={device.id} style={styles.enhancedDeviceCard}>
                      {/* Remove Button - White X at top right corner */}
                      <TouchableOpacity
                        style={styles.removeButtonTopRight}
                        onPress={() => removeDevice(device.id)}
                      >
                        <Text style={styles.removeButtonX}>×</Text>
                      </TouchableOpacity>

                      <View style={styles.deviceInfo}>
                        <Text style={styles.deviceCardName}>{device.name}</Text>
                        <Text style={styles.deviceCardType}>{device.type}</Text>
                        <View style={styles.deviceStatusRow}>
                          <View style={[styles.deviceStatusIndicator, device.status === 'connected' ? styles.connected : styles.disconnected]} />
                          <Text style={[styles.deviceStatusText, device.status === 'connected' ? styles.connectedText : styles.disconnectedText]}>
                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}
                          </Text>
                        </View>
                      </View>

                      {/* Connect/Disconnect Button - Bottom Right Corner */}
                      <TouchableOpacity
                        style={[styles.deviceActionButtonBottomRight, device.status === 'connected' ? styles.disconnectButton : styles.connectButton]}
                        onPress={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}
                      >
                        <Text style={styles.deviceActionText}>
                          {device.status === 'connected' ? 'Disconnect' : 'Connect'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  ))
                )}
              </View>

              {/* Device Discovery - EXACTLY FROM DEMO.HTML */}
              <View style={styles.settingsSection}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Device Discovery</Text>
                  <TouchableOpacity
                    style={[styles.discoverButton, isDiscovering && styles.discoverButtonDisabled]}
                    onPress={refreshDiscovery}
                    disabled={isDiscovering}
                  >
                    <Text style={styles.discoverButtonText}>
                      {isDiscovering ? '🔍 Scanning...' : 'Discover'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {networkDevices.length === 0 ? (
                  <View style={styles.emptyDeviceList}>
                    <Text style={styles.emptyDeviceText}>No devices found</Text>
                    <Text style={styles.emptyDeviceSubtext}>
                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}
                    </Text>
                  </View>
                ) : (
                  networkDevices.map((device) => (
                    <View key={device.id} style={styles.discoveredDeviceCard}>
                      <View style={styles.deviceInfo}>
                        <Text style={styles.deviceCardName}>{device.name}</Text>
                        <Text style={styles.deviceCardType}>{device.type}</Text>
                        <Text style={styles.deviceCardLastSeen}>{device.lastSeen}</Text>
                      </View>
                      <TouchableOpacity
                        style={styles.pairButton}
                        onPress={() => pairDevice(device.id)}
                      >
                        <Text style={styles.pairButtonText}>Pair</Text>
                      </TouchableOpacity>
                    </View>
                  ))
                )}

                <View style={styles.qrButtonsContainer}>
                  <TouchableOpacity style={styles.qrScanButton} onPress={scanQRCode}>
                    <Text style={styles.qrScanButtonText}>Scan QR</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.qrGenerateButton} onPress={generateQRCode}>
                    <Text style={styles.qrGenerateButtonText}>Generate QR</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Additional Settings - EXACTLY FROM DEMO.HTML */}
              <View style={styles.settingsSection}>
                <Text style={styles.sectionTitle}>Additional Settings</Text>
                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => setShowSyncSettings(true)}
                >
                  <Text style={styles.settingItemText}>Sync Settings</Text>
                  <Text style={styles.settingItemArrow}>›</Text>
                </TouchableOpacity>

                {/* Background Sync Setting */}
                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    const newValue = !backgroundSyncEnabled;
                    setBackgroundSyncEnabled(newValue);
                    backgroundSyncService.setEnabled(newValue);
                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');
                  }}
                >
                  <Text style={styles.settingItemText}>Background Sync</Text>
                  <Text style={[styles.settingItemArrow, { color: '#FFFFFF', fontWeight: 'bold' }]}>
                    {backgroundSyncEnabled ? 'on' : 'off'}
                  </Text>
                </TouchableOpacity>

                {/* Network Discovery Setting */}
                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    if (isDiscovering) {
                      networkDiscoveryService.stopDiscovery();
                      setIsDiscovering(false);
                      showMessage('Network discovery stopped');
                    } else {
                      networkDiscoveryService.startDiscovery();
                      setIsDiscovering(true);
                      showMessage('Network discovery started');
                    }
                  }}
                >
                  <Text style={styles.settingItemText}>Network Discovery</Text>
                  <Text style={[styles.settingItemArrow, { color: '#FFFFFF', fontWeight: 'bold' }]}>
                    {isDiscovering ? 'on' : 'off'}
                  </Text>
                </TouchableOpacity>

                {/* Discoverable Setting */}
                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={async () => {
                    try {
                      const newValue = !isDiscoverable;
                      setIsDiscoverable(newValue);

                      if (newValue) {
                        // Enable device broadcasting
                        console.log('Enabling device broadcasting...');

                        // Check if service is initialized
                        if (!networkDiscoveryService) {
                          showMessage('Network discovery service not available');
                          return;
                        }

                        // Start discovery first if not already running
                        if (!isDiscovering) {
                          await networkDiscoveryService.startDiscovery();
                          setIsDiscovering(true);
                        }

                        showMessage('Device is now discoverable by other Clipsy devices');
                      } else {
                        // Disable device broadcasting
                        console.log('Disabling device broadcasting...');
                        showMessage('Device is no longer discoverable');
                      }
                    } catch (error) {
                      console.error('Error toggling discoverable:', error);
                      showMessage(`Error: ${error.message || 'Unknown error'}`);
                      // Revert the state change on error
                      setIsDiscoverable(!isDiscoverable);
                    }
                  }}
                >
                  <Text style={styles.settingItemText}>Discoverable</Text>
                  <Text style={[styles.settingItemArrow, { color: '#FFFFFF', fontWeight: 'bold' }]}>
                    {isDiscoverable ? 'on' : 'off'}
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Sync Settings Modal - EXACTLY FROM DEMO.HTML */}
      <Modal
        visible={showSyncSettings}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSyncSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.syncSettingsModal}>
            <View style={styles.settingsHeader}>
              <Text style={styles.settingsTitle}>Sync Settings</Text>
              <TouchableOpacity onPress={() => setShowSyncSettings(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.settingsContent}>
              <View style={styles.settingsSection}>
                <View style={styles.syncSettingItem}>
                  <Text style={styles.syncSettingLabel}>Auto Sync</Text>
                  <Switch
                    value={syncSettings.autoSync}
                    onValueChange={(value) => setSyncSettings({...syncSettings, autoSync: value})}
                    trackColor={{ false: '#767577', true: '#4CAF50' }}
                    thumbColor={syncSettings.autoSync ? '#FFFFFF' : '#f4f3f4'}
                  />
                </View>

                <View style={styles.syncSettingItem}>
                  <Text style={styles.syncSettingLabel}>Sync Delay: {syncSettings.syncDelay} seconds</Text>
                  <Text style={styles.syncSettingDescription}>
                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}
                  </Text>
                  <View style={styles.syncDelayControls}>
                    <TouchableOpacity
                      style={styles.syncDelayButton}
                      onPress={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}
                    >
                      <Text style={styles.syncDelayButtonText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.syncDelayValue}>{syncSettings.syncDelay}s</Text>
                    <TouchableOpacity
                      style={styles.syncDelayButton}
                      onPress={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}
                    >
                      <Text style={styles.syncDelayButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.syncSettingItem}>
                  <Text style={styles.syncSettingLabel}>Sync on Connect</Text>
                  <Switch
                    value={syncSettings.syncOnConnect}
                    onValueChange={(value) => setSyncSettings({...syncSettings, syncOnConnect: value})}
                    trackColor={{ false: '#767577', true: '#4CAF50' }}
                    thumbColor={syncSettings.syncOnConnect ? '#FFFFFF' : '#f4f3f4'}
                  />
                </View>

                <View style={styles.syncSettingItem}>
                  <Text style={styles.syncSettingLabel}>Bidirectional Sync</Text>
                  <Switch
                    value={syncSettings.bidirectional}
                    onValueChange={(value) => setSyncSettings({...syncSettings, bidirectional: value})}
                    trackColor={{ false: '#767577', true: '#4CAF50' }}
                    thumbColor={syncSettings.bidirectional ? '#FFFFFF' : '#f4f3f4'}
                  />
                </View>



              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* QR Code Scanner Modal */}
      <Modal
        visible={isQRScannerVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setIsQRScannerVisible(false)}
      >
        {QRCodeScanner ? (
          <QRCodeScanner
            isVisible={isQRScannerVisible}
            onClose={() => setIsQRScannerVisible(false)}
            onScanSuccess={handleQRScanSuccess}
            onConnectionSuccess={handleQRConnectionSuccess}
          />
        ) : (
          <View style={styles.container}>
            <View style={styles.qrNotAvailableContainer}>
              <Text style={styles.qrNotAvailableTitle}>QR Scanner Not Available</Text>
              <Text style={styles.qrNotAvailableText}>
                QR code scanning is not available on this platform.
              </Text>
              <TouchableOpacity
                style={styles.qrNotAvailableButton}
                onPress={() => setIsQRScannerVisible(false)}
              >
                <Text style={styles.qrNotAvailableButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Modal>

      {/* QR Code Generator Modal */}
      <Modal
        visible={isQRGeneratorVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsQRGeneratorVisible(false)}
      >
        <View style={styles.qrModalOverlay}>
          <View style={styles.qrModalContainer}>
            <View style={styles.qrModalHeader}>
              <Text style={styles.qrModalTitle}>QR Code for Device Pairing</Text>
              <TouchableOpacity
                style={styles.qrModalCloseButton}
                onPress={() => setIsQRGeneratorVisible(false)}
              >
                <Text style={styles.qrModalCloseText}>×</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.qrCodeContainer}>
              {QRCode ? (
                <View style={styles.qrCodeWrapper}>
                  <QRCode
                    value={generateQRCodeData()}
                    size={200}
                    color="#1E40AF"
                    backgroundColor="#ffffff"
                  />
                  <Text style={styles.qrInstructions}>
                    📱 Scan this QR code with another Clipsy device to connect
                  </Text>
                  <Text style={styles.qrSubInstructions}>
                    Make sure both devices are on the same WiFi network
                  </Text>
                </View>
              ) : (
                <View style={styles.qrCodeFallback}>
                  <Text style={styles.qrCodeFallbackTitle}>📱 Device Connection</Text>
                  <Text style={styles.qrCodeFallbackText}>
                    QR code generation is not available on this platform.
                    {'\n\n'}Alternative connection methods:
                    {'\n'}• Use device discovery to find nearby devices
                    {'\n'}• Manually enter device IP address
                    {'\n'}• Connect devices on the same WiFi network
                  </Text>
                  <TouchableOpacity
                    style={styles.qrFallbackButton}
                    onPress={() => {
                      setIsQRGeneratorVisible(false);
                      // Optionally open device discovery
                      setShowSettings(true);
                    }}
                  >
                    <Text style={styles.qrFallbackButtonText}>Open Device Discovery</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            <Text style={styles.qrInstructions}>
              Scan this QR code with another Clipsy device to establish a connection
            </Text>

            <TouchableOpacity
              style={styles.qrModalButton}
              onPress={() => setIsQRGeneratorVisible(false)}
            >
              <Text style={styles.qrModalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Floating Overlay Status */}
      {isFloatingOverlayVisible && (
        <View style={styles.floatingOverlayStatus}>
          <Text style={styles.floatingOverlayStatusText}>
            📋 Android floating widget active - PC/Server clipboards accessible
          </Text>
          <TouchableOpacity
            style={styles.floatingOverlayStatusButton}
            onPress={toggleFloatingOverlay}
          >
            <Text style={styles.floatingOverlayStatusButtonText}>Hide</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Floating Clipboard Widget */}
      <FloatingClipboardWidget
        visible={isFloatingOverlayVisible}
        onClose={() => setIsFloatingOverlayVisible(false)}
        onCopy={(content) => {
          showMessage(`📋 Copied: ${content.substring(0, 30)}...`);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // EXACTLY FROM DEMO.HTML - ORIGINAL STYLES
  container: {
    flex: 1,
    backgroundColor: '#121212', // Original demo.html background
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20, // Extra padding at bottom for better scrolling
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appLogo: {
    width: 32,
    height: 32,
    marginRight: 8,
  },
  logoContainer: {
    position: 'relative',
    marginRight: 0,
  },
  connectionDot: {
    position: 'absolute',
    bottom: -2,
    left: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#201c1c',
  },
  pinIcon: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  pinHead: {
    width: 8,
    height: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    position: 'absolute',
    top: 0,
  },
  pinBody: {
    width: 2,
    height: 10,
    backgroundColor: '#FFFFFF',
    position: 'absolute',
    top: 6,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  iconButton: {
    padding: 8,
    borderRadius: 4,
  },
  iconButtonActive: {
    backgroundColor: '#2A2A2A',
  },
  iconButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  successMessage: {
    backgroundColor: '#4CAF50',
    padding: 12,
    marginHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  successMessageText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  // Live Preview - SHOWS ACTIVE CLIPBOARD
  livePreview: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    margin: 16,
  },
  livePreviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  livePreviewTitle: {
    color: '#E0E0E0',
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  sourceToggle: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 2,
  },
  sourceButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  sourceButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  sourceButtonText: {
    fontSize: 16,
    color: '#A0A0A0',
  },
  sourceButtonTextActive: {
    color: '#FFFFFF',
  },
  liveText: {
    color: '#E0E0E0',
    fontSize: 16,
    marginBottom: 12,
    lineHeight: 22,
  },
  actionIcons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  actionButton: {
    padding: 8,
  },
  actionButtonText: {
    fontSize: 20,
    color: '#E0E0E0',
  },
  // History - EXACTLY FROM DEMO.HTML
  historyTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#A0A0A0',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  historyList: {
    flex: 1,
    marginHorizontal: 16,
  },
  historyItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#A0A0A0',
    flex: 1,
  },
  deleteButton: {
    padding: 4,
    marginLeft: 8,
  },
  deleteButtonText: {
    color: '#FFFFFF', // WHITE DELETE X
    fontSize: 14,
    fontWeight: 'bold',
    lineHeight: 16,
  },
  itemContent: {
    color: '#E0E0E0',
    fontSize: 14,
    lineHeight: 18,
  },
  // Floating Sync Button - Circular with Shadow
  floatingSyncButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,                      // Moved to left side
    width: 64,                     // Circle container size
    height: 64,                    // Circle container size
    borderRadius: 32,              // Perfect circle (half of width/height)
    backgroundColor: '#2a2a2a',    // Dark gray background for the circle
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    // Soft shadow effect for round button
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 6,                  // Android shadow
  },
  syncIcon: {
    width: 40,                     // Increased icon size inside the circle
    height: 40,                    // Increased icon size inside the circle
    tintColor: '#8B5CF6',          // Apply the exact color to the PNG
  },
  // Modal Styles - EXACTLY FROM DEMO.HTML
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  settingsSidebar: {
    backgroundColor: '#1E1E1E',
    height: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  syncSettingsModal: {
    backgroundColor: '#1E1E1E',
    height: '60%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  settingsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  settingsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    fontSize: 24,
    color: '#A0A0A0',
    padding: 5,
  },
  settingsContent: {
    flex: 1,
  },
  settingsSection: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#E0E0E0',
    marginBottom: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  discoverButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  discoverButtonDisabled: {
    backgroundColor: '#666666',
    opacity: 0.7,
  },
  discoverButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  deviceInfoCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E0E0E0',
    marginBottom: 5,
  },
  deviceDetail: {
    fontSize: 14,
    color: '#A0A0A0',
    marginBottom: 3,
  },
  deviceCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  enhancedDeviceCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
    minHeight: 80,
  },
  emptyDeviceList: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  emptyDeviceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#A0A0A0',
    marginBottom: 5,
  },
  emptyDeviceSubtext: {
    fontSize: 14,
    color: '#808080',
    textAlign: 'center',
  },
  deviceStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  deviceStatusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  deviceStatusText: {
    fontSize: 12,
    flex: 1,
  },
  connectedText: {
    color: '#4CAF50',
  },
  disconnectedText: {
    color: '#f44336',
  },
  connectButton: {
    backgroundColor: '#8B5CF6',
  },
  disconnectButton: {
    backgroundColor: '#8B5CF6',
  },
  removeButtonTopRight: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  removeButtonX: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 18,
  },
  deviceActionButtonBottomRight: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  discoveredDeviceCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deviceInfo: {
    flex: 1,
  },
  deviceCardName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E0E0E0',
    marginBottom: 3,
  },
  deviceCardType: {
    fontSize: 14,
    color: '#A0A0A0',
    marginBottom: 3,
  },
  deviceCardLastSeen: {
    fontSize: 12,
    color: '#808080',
  },
  deviceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  deviceStatus: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  connected: {
    backgroundColor: '#4CAF50',
  },
  disconnected: {
    backgroundColor: '#f44336',
  },
  statusText: {
    fontSize: 12,
    color: '#FFFFFF',
  },
  deviceActionButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 6,
  },
  deviceActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  removeButton: {
    backgroundColor: '#f44336',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 6,
  },
  removeButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  pairButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
  },
  pairButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  qrButtonsContainer: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  qrScanButton: {
    backgroundColor: '#8B5CF6',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
  },
  qrScanButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  qrGenerateButton: {
    backgroundColor: '#8B5CF6',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
  },
  qrGenerateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  settingItem: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingItemText: {
    fontSize: 16,
    color: '#E0E0E0',
  },
  settingItemArrow: {
    fontSize: 18,
    color: '#A0A0A0',
  },
  syncSettingItem: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  syncSettingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E0E0E0',
    marginBottom: 5,
  },
  syncSettingDescription: {
    fontSize: 14,
    color: '#A0A0A0',
  },
  syncDelayControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    gap: 15,
  },
  syncDelayButton: {
    backgroundColor: '#4CAF50',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  syncDelayButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  syncDelayValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E0E0E0',
    minWidth: 40,
    textAlign: 'center',
  },
  // NEW DEVICE SECTIONS STYLES - WHITE EDIT UI
  deviceSection: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginBottom: 8,
  },
  deviceSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    flexWrap: 'nowrap',
  },
  deviceSectionTitleContainer: {
    flex: 1,
  },
  deviceSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E0E0E0',
  },
  deviceSectionSubtitle: {
    fontSize: 14,
    color: '#A0A0A0',
    marginTop: 2,
  },
  refreshClipboardButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  refreshClipboardButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  deviceClipboard: {
    marginBottom: 8,
  },
  clipboardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clipboardLabel: {
    fontSize: 16,
    color: '#A0A0A0',
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButtonIcon: {
    color: '#FFFFFF', // WHITE PENCIL ICON
    fontSize: 20,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  editButtonInside: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    padding: 4,
  },
  clipboardContent: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    position: 'relative', // For absolute positioned edit button
  },
  thisDeviceContent: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)', // Green theme for This Device
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  connectedDeviceContent: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)', // Blue theme for Connected Device
    borderColor: 'rgba(33, 150, 243, 0.3)',
  },
  clipboardText: {
    color: '#E0E0E0',
    fontSize: 16,
    marginBottom: 8,
    lineHeight: 22,
  },
  clipboardMeta: {
    fontSize: 12,
    color: '#A0A0A0',
    fontStyle: 'italic',
  },
  // Edit Container Styles - WHITE UI
  editContainer: {
    marginBottom: 12,
  },
  editTextInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 16,
    color: '#E0E0E0',
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  saveButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  saveButtonIcon: {
    color: '#FFFFFF', // WHITE SAVE ICON
    fontSize: 16,
  },
  saveButtonText: {
    color: '#FFFFFF', // WHITE SAVE TEXT
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  cancelButtonIcon: {
    color: '#FFFFFF', // WHITE CANCEL ICON
    fontSize: 16,
  },
  cancelButtonText: {
    color: '#FFFFFF', // WHITE CANCEL TEXT
    fontSize: 16,
    fontWeight: '600',
  },
  // THEMED BUTTONS - MATCH DEVICE COLORS
  saveButtonThisDevice: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)', // Green theme for This Device
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonThisDevice: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)', // Red theme for Cancel
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonConnectedDevice: {
    backgroundColor: 'rgba(33, 150, 243, 0.2)', // Blue theme for Connected Device
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonConnectedDevice: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)', // Red theme for Cancel
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.4)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // QR Code Generator Modal Styles
  qrModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrModalContainer: {
    backgroundColor: '#201c1c',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    maxWidth: 350,
    width: '90%',
  },
  qrModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
  },
  qrModalTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  qrModalCloseButton: {
    padding: 5,
  },
  qrModalCloseText: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  qrCodeContainer: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 240,
  },
  qrCodeWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrInstructions: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  qrSubInstructions: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  qrCodeFallback: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
    height: 200,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8B5CF6',
    borderStyle: 'dashed',
  },
  qrCodeFallbackTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8B5CF6',
    marginBottom: 10,
  },
  qrCodeFallbackText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 16,
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  qrFallbackButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginTop: 10,
  },
  qrFallbackButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  qrInstructions: {
    color: '#ffffff',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  qrModalButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  qrModalButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // QR Not Available Styles
  qrNotAvailableContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
    padding: 20,
  },
  qrNotAvailableTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  qrNotAvailableText: {
    color: '#E0E0E0',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  qrNotAvailableButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  qrNotAvailableButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Floating Overlay Status Styles
  floatingOverlayStatus: {
    position: 'absolute',
    top: 100,
    left: 16,
    right: 16,
    backgroundColor: '#8B5CF6',
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  floatingOverlayStatusText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  floatingOverlayStatusButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  floatingOverlayStatusButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  // Cross-Platform Sync Button Styles
  crossPlatformSyncButton: {
    backgroundColor: '#8B5CF6',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginVertical: 4,
  },
  crossPlatformSyncButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  // Settings Description and Note Styles
  settingsDescription: {
    fontSize: 14,
    color: '#A0A0A0',
    marginBottom: 16,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  settingsNote: {
    fontSize: 12,
    color: '#808080',
    marginTop: 16,
    padding: 12,
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    lineHeight: 18,
    borderLeftWidth: 3,
    borderLeftColor: '#8B5CF6',
  },
});

export default DemoClipsyInterface;
