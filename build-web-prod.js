/**
 * Build script for production web build
 * This ensures the web build is created in the correct location
 */

// Set environment variables
process.env.NODE_ENV = 'production';
process.env.EXPO_NO_TYPESCRIPT_SETUP = true;
process.env.NODE_OPTIONS = '--openssl-legacy-provider';

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Ensure we're in the mobile directory
const currentDir = process.cwd();
const isMobileDir = currentDir.endsWith('mobile');
const rootDir = isMobileDir ? path.resolve('..') : process.cwd();
const mobileDir = isMobileDir ? process.cwd() : path.join(rootDir, 'mobile');

// Print build info
console.log('📱 Building Web App for Production');
console.log(`📂 Root directory: ${rootDir}`);
console.log(`📂 Mobile directory: ${mobileDir}`);

try {
  // Ensure AppWeb.js exists
  const appWebPath = path.join(mobileDir, 'AppWeb.js');
  if (!fs.existsSync(appWebPath)) {
    console.log('⚠️ AppWeb.js not found. Creating optimized entry point...');
    const appWebContent = `import React, { lazy, Suspense } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Lazy load the main app component
const LazyApp = lazy(() => import('./App'));

// Loading component to show while the main app is loading
const LoadingComponent = () => (
  <View style={styles.container}>
    <Text style={styles.text}>Loading ClipBee...</Text>
  </View>
);

// Styles for the loading component
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  text: {
    fontSize: 18,
    color: '#333',
    marginBottom: 20,
  },
});

// Web-optimized entry point
export default function AppWeb() {
  return (
    <Suspense fallback={<LoadingComponent />}>
      <LazyApp />
    </Suspense>
  );
}
`;
    fs.writeFileSync(appWebPath, appWebContent);
    console.log('✅ AppWeb.js created successfully');
  }

  // Update main.js file to use AppWeb for web platform
  const mainPath = path.join(mobileDir, 'index.js');
  const mainContent = fs.readFileSync(mainPath, 'utf8');
  
  if (!mainContent.includes('AppWeb')) {
    console.log('⚠️ Updating index.js to use platform-specific entry points...');
    const updatedMainContent = `import { Platform } from 'react-native';
import { registerRootComponent } from 'expo';
import App from './App';

// Use platform-specific entry point for web
const AppEntry = Platform.OS === 'web' 
  ? require('./AppWeb').default
  : App;

// Register the root component
registerRootComponent(AppEntry);
`;
    fs.writeFileSync(mainPath, updatedMainContent);
    console.log('✅ Updated index.js with platform-specific entry');
  }
  
  // Run expo build:web with the correct settings
  console.log('🚀 Building web application...');
  const buildCommand = 'npx expo export:web';
  
  // Execute the build command
  execSync(buildCommand, { 
    stdio: 'inherit',
    cwd: mobileDir,
    env: {
      ...process.env,
      NODE_ENV: 'production',
      EXPO_NO_TYPESCRIPT_SETUP: 'true',
      NODE_OPTIONS: '--openssl-legacy-provider'
    }
  });
  
  console.log('✅ Web build completed successfully!');
  console.log('📋 You can serve the app with: npx serve -s web-build -l 4000');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
