import React, { lazy, Suspense } from 'react';

/**
 * Creates a dynamically imported component with loading state
 * @param {Function} importFunc - Dynamic import function
 * @param {React.ComponentType} LoadingComponent - Component to show while loading
 * @returns {React.ComponentType} - Lazy-loaded component with suspense
 */
export const dynamicImport = (importFunc, LoadingComponent = () => null) => {
  const LazyComponent = lazy(importFunc);
  
  return props => (
    <Suspense fallback={<LoadingComponent />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};
