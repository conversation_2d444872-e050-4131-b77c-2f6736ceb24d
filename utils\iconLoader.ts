/**
 * Icon Loader Utility
 * 
 * This utility helps optimize the loading of MaterialIcons font
 * by implementing dynamic imports and caching.
 */

import { Platform } from 'react-native';

// Cache for loaded icons to prevent duplicate imports
const iconCache: Record<string, any> = {};

/**
 * Dynamically load a specific icon from MaterialIcons
 * This reduces the initial bundle size by only loading
 * the icons that are actually used in the application
 */
export const loadIcon = async (iconName: string) => {
  // Return from cache if already loaded
  if (iconCache[iconName]) {
    return iconCache[iconName];
  }

  try {
    // Different loading strategy based on platform
    if (Platform.OS === 'web') {
      // For web, dynamically import only the specific icon
      const MaterialIcons = await import('react-native-vector-icons/MaterialIcons');
      const IconComponent = MaterialIcons.default;
      
      // Cache the icon for future use
      iconCache[iconName] = {
        component: IconComponent,
        name: iconName
      };
      
      return iconCache[iconName];
    } else {
      // For native platforms, load the entire icon set (more efficient on native)
      const MaterialIcons = require('react-native-vector-icons/MaterialIcons').default;
      
      // Cache the icon for future use
      iconCache[iconName] = {
        component: MaterialIcons,
        name: iconName
      };
      
      return iconCache[iconName];
    }
  } catch (error) {
    console.error('Failed to load icon:', iconName, error);
    return null;
  }
};

/**
 * Create an optimized icon component that can be used as a drop-in replacement
 * for MaterialIcons with lazy loading capabilities
 */
export const createIconComponent = async (
  iconName: string, 
  size: number = 24, 
  color: string = '#000000'
) => {
  const icon = await loadIcon(iconName);
  
  if (!icon) {
    return null;
  }
  
  const { component: IconComponent, name } = icon;
  return { IconComponent, name, size, color };
};
