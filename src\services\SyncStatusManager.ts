/**
 * Sync Status Manager
 * Manages sync status tracking, conflict resolution, and overall sync coordination
 */

import { SyncDevice, SyncStatus, ClipboardSyncItem } from './CrossPlatformSyncProtocol';
import { androidToAndroidSync } from './AndroidToAndroidSync';
import { windowsToWindowsSync } from './WindowsToWindowsSync';
import { androidToWindowsSync } from './AndroidToWindowsSync';
import { syncDiscoveryService } from './SyncDiscoveryService';

export interface SyncStatusInfo {
  deviceId: string;
  deviceName: string;
  platform: 'android' | 'windows';
  status: SyncStatus;
  lastSyncTime: number;
  syncCount: number;
  errorCount: number;
  lastError?: string;
  latency: number;
  bytesTransferred: number;
}

export interface ConflictInfo {
  id: string;
  timestamp: number;
  sourceDevice: string;
  targetDevice: string;
  conflictType: 'content-mismatch' | 'timestamp-conflict' | 'format-incompatible';
  sourceContent: string;
  targetContent: string;
  resolution: 'pending' | 'resolved' | 'ignored';
  resolutionMethod?: 'source-wins' | 'target-wins' | 'merge' | 'manual';
}

export interface SyncManagerConfiguration {
  enableConflictResolution: boolean;
  defaultConflictResolution: 'source-wins' | 'target-wins' | 'latest-wins' | 'manual';
  maxConflictHistory: number;
  syncTimeout: number;
  retryAttempts: number;
  statusUpdateInterval: number;
  enableSyncLogging: boolean;
  logRetentionDays: number;
}

export interface OverallSyncStats {
  totalDevices: number;
  connectedDevices: number;
  androidDevices: number;
  windowsDevices: number;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  conflictsDetected: number;
  conflictsResolved: number;
  averageLatency: number;
  totalBytesTransferred: number;
  uptime: number;
  lastSyncTime: number;
}

export class SyncStatusManager {
  private config: SyncManagerConfiguration;
  private deviceStatuses: Map<string, SyncStatusInfo> = new Map();
  private conflicts: Map<string, ConflictInfo> = new Map();
  private syncLogs: Array<{timestamp: number, event: string, details: any}> = [];
  private startTime: number = Date.now();
  private statusTimer: NodeJS.Timeout | null = null;
  private callbacks: {
    onStatusUpdate?: (deviceId: string, status: SyncStatusInfo) => void;
    onConflictDetected?: (conflict: ConflictInfo) => void;
    onConflictResolved?: (conflict: ConflictInfo) => void;
    onSyncComplete?: (deviceId: string, success: boolean) => void;
    onOverallStatsUpdate?: (stats: OverallSyncStats) => void;
  } = {};

  constructor(config: Partial<SyncManagerConfiguration> = {}) {
    this.config = {
      enableConflictResolution: true,
      defaultConflictResolution: 'latest-wins',
      maxConflictHistory: 100,
      syncTimeout: 30000, // 30 seconds
      retryAttempts: 3,
      statusUpdateInterval: 5000, // 5 seconds
      enableSyncLogging: true,
      logRetentionDays: 7,
      ...config
    };
  }

  /**
   * Initialize sync status manager
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    this.callbacks = callbacks;

    // Initialize sync services with callbacks
    await this.initializeSyncServices();

    // Start status monitoring
    this.startStatusMonitoring();

    // Initialize device statuses
    await this.initializeDeviceStatuses();

    console.log('Sync Status Manager initialized');
  }

  /**
   * Initialize sync services with status callbacks
   */
  private async initializeSyncServices(): Promise<void> {
    // Initialize Android to Android sync
    await androidToAndroidSync.initialize({
      onSyncSuccess: (device, item) => this.handleSyncSuccess(device.id, 'android-to-android', item),
      onSyncFailure: (device, error) => this.handleSyncFailure(device.id, 'android-to-android', error),
      onClipboardChange: (content) => this.logEvent('clipboard-change', { platform: 'android', content: content.substring(0, 50) + '...' }),
      onDeviceConnected: (device) => this.handleDeviceConnected(device),
      onDeviceDisconnected: (device) => this.handleDeviceDisconnected(device)
    });

    // Initialize Windows to Windows sync
    await windowsToWindowsSync.initialize({
      onSyncSuccess: (device, item) => this.handleSyncSuccess(device.id, 'windows-to-windows', item),
      onSyncFailure: (device, error) => this.handleSyncFailure(device.id, 'windows-to-windows', error),
      onClipboardChange: (content, format) => this.logEvent('clipboard-change', { platform: 'windows', format, content: content.substring(0, 50) + '...' }),
      onFileSync: (fileName, size) => this.logEvent('file-sync', { fileName, size }),
      onRichTextSync: (content, format) => this.logEvent('rich-text-sync', { format, size: content.length })
    });

    // Initialize Android to Windows sync
    await androidToWindowsSync.initialize({
      onAndroidToWindowsSync: (device, item) => this.handleSyncSuccess(device.id, 'android-to-windows', item),
      onWindowsToAndroidSync: (device, item) => this.handleSyncSuccess(device.id, 'windows-to-android', item),
      onSyncFailure: (direction, error) => this.handleCrossPlatformSyncFailure(direction, error),
      onFormatConversion: (from, to, content) => this.logEvent('format-conversion', { from, to, size: content.length }),
      onConflictDetected: (androidContent, windowsContent) => this.handleConflictDetected('android', 'windows', androidContent, windowsContent),
      onSyncSuccess: (direction, device) => this.logEvent('cross-platform-sync', { direction, device: device.name })
    });
  }

  /**
   * Initialize device statuses
   */
  private async initializeDeviceStatuses(): Promise<void> {
    const allDevices = syncDiscoveryService.getAllDevices();
    
    for (const device of allDevices) {
      const statusInfo: SyncStatusInfo = {
        deviceId: device.id,
        deviceName: device.name,
        platform: device.platform,
        status: device.status,
        lastSyncTime: 0,
        syncCount: 0,
        errorCount: 0,
        latency: 0,
        bytesTransferred: 0
      };
      
      this.deviceStatuses.set(device.id, statusInfo);
    }
  }

  /**
   * Handle sync success
   */
  private handleSyncSuccess(deviceId: string, syncType: string, item: ClipboardSyncItem): void {
    const status = this.deviceStatuses.get(deviceId);
    if (status) {
      status.lastSyncTime = Date.now();
      status.syncCount++;
      status.bytesTransferred += item.size;
      status.status = 'connected';
      
      this.deviceStatuses.set(deviceId, status);
      this.callbacks.onStatusUpdate?.(deviceId, status);
      this.callbacks.onSyncComplete?.(deviceId, true);
    }

    this.logEvent('sync-success', { deviceId, syncType, size: item.size });
  }

  /**
   * Handle sync failure
   */
  private handleSyncFailure(deviceId: string, syncType: string, error: string): void {
    const status = this.deviceStatuses.get(deviceId);
    if (status) {
      status.errorCount++;
      status.lastError = error;
      status.status = 'error';
      
      this.deviceStatuses.set(deviceId, status);
      this.callbacks.onStatusUpdate?.(deviceId, status);
      this.callbacks.onSyncComplete?.(deviceId, false);
    }

    this.logEvent('sync-failure', { deviceId, syncType, error });
  }

  /**
   * Handle cross-platform sync failure
   */
  private handleCrossPlatformSyncFailure(direction: string, error: string): void {
    this.logEvent('cross-platform-sync-failure', { direction, error });
  }

  /**
   * Handle device connected
   */
  private handleDeviceConnected(device: SyncDevice): void {
    const status = this.deviceStatuses.get(device.id);
    if (status) {
      status.status = 'connected';
      this.deviceStatuses.set(device.id, status);
      this.callbacks.onStatusUpdate?.(device.id, status);
    }

    this.logEvent('device-connected', { deviceId: device.id, deviceName: device.name, platform: device.platform });
  }

  /**
   * Handle device disconnected
   */
  private handleDeviceDisconnected(device: SyncDevice): void {
    const status = this.deviceStatuses.get(device.id);
    if (status) {
      status.status = 'disconnected';
      this.deviceStatuses.set(device.id, status);
      this.callbacks.onStatusUpdate?.(device.id, status);
    }

    this.logEvent('device-disconnected', { deviceId: device.id, deviceName: device.name });
  }

  /**
   * Handle conflict detected
   */
  private handleConflictDetected(sourcePlatform: string, targetPlatform: string, sourceContent: string, targetContent: string): void {
    const conflictId = `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const conflict: ConflictInfo = {
      id: conflictId,
      timestamp: Date.now(),
      sourceDevice: sourcePlatform,
      targetDevice: targetPlatform,
      conflictType: 'content-mismatch',
      sourceContent,
      targetContent,
      resolution: 'pending'
    };

    this.conflicts.set(conflictId, conflict);
    this.callbacks.onConflictDetected?.(conflict);

    // Auto-resolve based on configuration
    if (this.config.enableConflictResolution) {
      this.resolveConflict(conflictId, this.config.defaultConflictResolution);
    }

    this.logEvent('conflict-detected', { conflictId, sourcePlatform, targetPlatform });
  }

  /**
   * Resolve conflict
   */
  resolveConflict(conflictId: string, method: 'source-wins' | 'target-wins' | 'latest-wins' | 'manual'): boolean {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      return false;
    }

    conflict.resolution = 'resolved';
    conflict.resolutionMethod = method;

    this.conflicts.set(conflictId, conflict);
    this.callbacks.onConflictResolved?.(conflict);

    this.logEvent('conflict-resolved', { conflictId, method });
    return true;
  }

  /**
   * Start status monitoring
   */
  private startStatusMonitoring(): void {
    this.statusTimer = setInterval(() => {
      this.updateOverallStats();
      this.cleanupOldLogs();
    }, this.config.statusUpdateInterval);

    console.log(`Status monitoring started (interval: ${this.config.statusUpdateInterval}ms)`);
  }

  /**
   * Update overall statistics
   */
  private updateOverallStats(): void {
    const allDevices = syncDiscoveryService.getAllDevices();
    const connectedDevices = allDevices.filter(d => d.status === 'connected');
    
    const totalSyncs = Array.from(this.deviceStatuses.values()).reduce((sum, status) => sum + status.syncCount, 0);
    const totalErrors = Array.from(this.deviceStatuses.values()).reduce((sum, status) => sum + status.errorCount, 0);
    const totalBytes = Array.from(this.deviceStatuses.values()).reduce((sum, status) => sum + status.bytesTransferred, 0);
    
    const stats: OverallSyncStats = {
      totalDevices: allDevices.length,
      connectedDevices: connectedDevices.length,
      androidDevices: allDevices.filter(d => d.platform === 'android').length,
      windowsDevices: allDevices.filter(d => d.platform === 'windows').length,
      totalSyncs,
      successfulSyncs: totalSyncs - totalErrors,
      failedSyncs: totalErrors,
      conflictsDetected: this.conflicts.size,
      conflictsResolved: Array.from(this.conflicts.values()).filter(c => c.resolution === 'resolved').length,
      averageLatency: this.calculateAverageLatency(),
      totalBytesTransferred: totalBytes,
      uptime: Date.now() - this.startTime,
      lastSyncTime: Math.max(...Array.from(this.deviceStatuses.values()).map(s => s.lastSyncTime), 0)
    };

    this.callbacks.onOverallStatsUpdate?.(stats);
  }

  /**
   * Calculate average latency
   */
  private calculateAverageLatency(): number {
    const latencies = Array.from(this.deviceStatuses.values()).map(s => s.latency).filter(l => l > 0);
    return latencies.length > 0 ? latencies.reduce((sum, l) => sum + l, 0) / latencies.length : 0;
  }

  /**
   * Log event
   */
  private logEvent(event: string, details: any): void {
    if (!this.config.enableSyncLogging) {
      return;
    }

    this.syncLogs.push({
      timestamp: Date.now(),
      event,
      details
    });

    console.log(`[SyncManager] ${event}:`, details);
  }

  /**
   * Cleanup old logs
   */
  private cleanupOldLogs(): void {
    const cutoffTime = Date.now() - (this.config.logRetentionDays * 24 * 60 * 60 * 1000);
    this.syncLogs = this.syncLogs.filter(log => log.timestamp > cutoffTime);

    // Cleanup old conflicts
    if (this.conflicts.size > this.config.maxConflictHistory) {
      const sortedConflicts = Array.from(this.conflicts.entries()).sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toRemove = sortedConflicts.slice(0, this.conflicts.size - this.config.maxConflictHistory);
      toRemove.forEach(([id]) => this.conflicts.delete(id));
    }
  }

  /**
   * Get device status
   */
  getDeviceStatus(deviceId: string): SyncStatusInfo | undefined {
    return this.deviceStatuses.get(deviceId);
  }

  /**
   * Get all device statuses
   */
  getAllDeviceStatuses(): SyncStatusInfo[] {
    return Array.from(this.deviceStatuses.values());
  }

  /**
   * Get conflicts
   */
  getConflicts(): ConflictInfo[] {
    return Array.from(this.conflicts.values());
  }

  /**
   * Get pending conflicts
   */
  getPendingConflicts(): ConflictInfo[] {
    return Array.from(this.conflicts.values()).filter(c => c.resolution === 'pending');
  }

  /**
   * Get sync logs
   */
  getSyncLogs(limit?: number): Array<{timestamp: number, event: string, details: any}> {
    const logs = [...this.syncLogs].reverse(); // Most recent first
    return limit ? logs.slice(0, limit) : logs;
  }

  /**
   * Force sync all devices
   */
  async forceSyncAll(): Promise<void> {
    console.log('Force syncing all devices...');
    
    try {
      await Promise.all([
        androidToAndroidSync.forceSyncNow(),
        windowsToWindowsSync.forceSyncNow(),
        androidToWindowsSync.forceSyncNow()
      ]);
      
      this.logEvent('force-sync-all', { timestamp: Date.now() });
    } catch (error) {
      this.logEvent('force-sync-all-failed', { error: error.toString() });
    }
  }

  /**
   * Update configuration
   */
  updateConfiguration(newConfig: Partial<SyncManagerConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.statusUpdateInterval !== undefined) {
      if (this.statusTimer) {
        clearInterval(this.statusTimer);
      }
      this.startStatusMonitoring();
    }
  }

  /**
   * Stop sync status manager
   */
  async stop(): Promise<void> {
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
      this.statusTimer = null;
    }

    await Promise.all([
      androidToAndroidSync.stop(),
      windowsToWindowsSync.stop(),
      androidToWindowsSync.stop()
    ]);

    console.log('Sync Status Manager stopped');
  }
}

// Export singleton instance
export const syncStatusManager = new SyncStatusManager();
export default SyncStatusManager;
