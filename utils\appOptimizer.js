/**
 * App Optimizer
 * Provides utilities to optimize app loading and performance
 */

// Control which features to load eagerly vs. lazily
export const FeatureFlags = {
  // Features to load immediately
  EAGER_LOAD: {
    CLIPBOARD_MONITORING: true,
    CORE_UI: true,
  },
  
  // Features to load only when needed
  LAZY_LOAD: {
    HISTORY_VIEWER: true,
    SETTINGS: true,
    QR_SCANNER: true,
  }
};

/**
 * <PERSON><PERSON> imports a module only when needed
 * @param {Function} importFn - Dynamic import function
 * @returns {Promise<any>} - Promise that resolves with the module
 */
export const lazyImport = (importFn) => {
  // Add a small delay to ensure UI remains responsive during import
  return new Promise(resolve => {
    setTimeout(() => {
      importFn().then(resolve);
    }, 50);
  });
};

/**
 * Preloads critical modules in the background
 * Call this during app initialization
 */
export const preloadCriticalModules = () => {
  // Queue important modules to load in the background
  // This uses requestIdleCallback (or setTimeout fallback) to load during idle time
  const idleCallback = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));
  
  idleCallback(() => {
    if (FeatureFlags.EAGER_LOAD.CLIPBOARD_MONITORING) {
      import('../services/clipboardService');
    }
  });
};

/**
 * Creates a lightweight placeholder while the real component loads
 * @param {string} componentType - Type of component to create placeholder for
 * @returns {Object} - Placeholder component props
 */
export const createPlaceholder = (componentType) => {
  switch (componentType) {
    case 'icon':
      return {
        width: 24,
        height: 24,
        backgroundColor: 'transparent',
      };
    case 'card':
      return {
        width: '100%',
        height: 80,
        backgroundColor: '#1E1E1E',
        borderRadius: 12,
      };
    default:
      return {};
  }
};
