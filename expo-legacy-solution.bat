@echo off
echo Starting ClipBee mobile app with proven working configuration...

:: Kill any existing Expo processes
taskkill /f /im node.exe /fi "WINDOWTITLE eq expo*" >nul 2>&1

:: Set environment variables based on previously working setup
set EXPO_NO_TYPESCRIPT_SETUP=1
set NODE_OPTIONS=--openssl-legacy-provider

echo.
echo =================================================================
echo IMPORTANT: Based on your project's history, this command should work.
echo If you still get manifest errors:
echo 1. Make sure you're using Expo Go that matches your SDK version (SDK 48)
echo 2. Completely close and restart the Expo Go app on your device
echo 3. Try the URL shown in terminal rather than the QR code
echo =================================================================
echo.

:: This is the exact command that worked before in your project history
npx expo-cli start --no-dev
