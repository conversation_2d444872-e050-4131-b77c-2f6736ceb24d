@echo off
echo Starting ClipBee Test Build for Android with Expo Go...
echo.
echo This build includes the new clipboard monitoring functionality
echo that will detect when text is copied on your device.
echo.
echo Instructions:
echo 1. Make sure Expo Go is installed on your Android device
echo 2. Connect your device to the same network as this computer
echo 3. Test the clipboard monitoring using the "Test Clipboard" button
echo.

:: Use the environment variables that have worked previously for this project
set EXPO_NO_TYPESCRIPT_SETUP=1
set NODE_OPTIONS=--openssl-legacy-provider

:: Clear the cache to ensure a clean build
echo Clearing Metro cache...
if exist node_modules\.cache\metro rmdir /s /q node_modules\.cache\metro

:: Start using the legacy expo-cli that has worked reliably for this project
echo Starting Expo server (this may take a moment)...
npx expo-cli start --no-dev
