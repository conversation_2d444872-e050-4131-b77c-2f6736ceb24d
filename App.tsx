import React, { useState } from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import the fully functional Clipsy interface
import DemoClipsyInterface from './src/components/DemoClipsyInterface';

// Use the simple loading screen for better compatibility
import SimpleLoadingScreen from './src/components/SimpleLoadingScreen';

console.log('App.tsx: Full Clipsy Android app starting...');

export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  console.log('App.tsx: Full Clipsy app component mounted');

  const handleLoadingComplete = () => {
    console.log('App.tsx: Loading completed, showing main interface');
    setIsLoading(false);
  };

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="light-content" backgroundColor="#121212" />
      {isLoading ? (
        <SimpleLoadingScreen
          onLoadingComplete={handleLoadingComplete}
          loadingDuration={3000}
        />
      ) : (
        <DemoClipsyInterface />
      )}
    </SafeAreaProvider>
  );
}

