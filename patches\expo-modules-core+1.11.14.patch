diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/.transforms/2c8077402800ba050708eac3902ffa15/results.bin b/node_modules/expo-modules-core/android-annotation-processor/build/.transforms/2c8077402800ba050708eac3902ffa15/results.bin
new file mode 100644
index 0000000..11e4581
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation-processor/build/.transforms/2c8077402800ba050708eac3902ffa15/results.bin
@@ -0,0 +1 @@
+o/jetified-expo-modules-core$android-annotation-processor-1.1.1
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation-processor.kotlin_module b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation-processor.kotlin_module
new file mode 100644
index 0000000..1e9f2ca
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation-processor.kotlin_module differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ConverterBinderVisitor.class b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ConverterBinderVisitor.class
new file mode 100644
index 0000000..7c4cf2d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ConverterBinderVisitor.class differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor$process$$inlined$filterIsInstance$1.class b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor$process$$inlined$filterIsInstance$1.class
new file mode 100644
index 0000000..42ef8b0
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor$process$$inlined$filterIsInstance$1.class differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor.class b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor.class
new file mode 100644
index 0000000..0878393
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessor.class differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessorProvider.class b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessorProvider.class
new file mode 100644
index 0000000..5670613
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/classes/kotlin/main/expo/modules/annotationprocessor/ExpoSymbolProcessorProvider.class differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..a6fefcf
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..aace5c7
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..689ec48
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..890effe
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..616c97b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..60020d8
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..db4fe84
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..991997b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fe23964
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..deea476
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..5f02255
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..db4fe84
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..991997b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..5c8f61c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..deea476
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..da73a6c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..d585a2c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..8aaef53
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..d78bf88
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..a54b756
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..67f7a5e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..9df22d9
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..d2de2a6
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..15e7146
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..eecea33
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..0f3b8b1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..4029703
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..689ec48
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..3c890a0
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..0813652
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab
new file mode 100644
index 0000000..a217c69
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream
new file mode 100644
index 0000000..b1477ce
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
new file mode 100644
index 0000000..c1c02f7
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at
new file mode 100644
index 0000000..6a7de3a
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i
new file mode 100644
index 0000000..744d4aa
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab
new file mode 100644
index 0000000..15a701d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream
new file mode 100644
index 0000000..db4fe84
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
new file mode 100644
index 0000000..991997b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at
new file mode 100644
index 0000000..54284b2
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i
new file mode 100644
index 0000000..deea476
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..2ceb12b
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+2
+0
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..bec534b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..4029703
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..689ec48
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..7d30a43
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..7a8217b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..227c1de
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..100d205
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..ccfcbf4
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..4efca36
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i
new file mode 100644
index 0000000..f768a77
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..b7a0f6f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..a01ab50
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..1a44ec4
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..5f5657b
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..9732429
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..33af781
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/last-build.bin b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/last-build.bin
new file mode 100644
index 0000000..e65a6eb
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/cacheable/last-build.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin
new file mode 100644
index 0000000..99b3630
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/local-state/build-history.bin b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/local-state/build-history.bin
new file mode 100644
index 0000000..04c95c7
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/kotlin/compileKotlin/local-state/build-history.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/libs/expo-modules-core$android-annotation-processor-1.1.1.jar b/node_modules/expo-modules-core/android-annotation-processor/build/libs/expo-modules-core$android-annotation-processor-1.1.1.jar
new file mode 100644
index 0000000..87b7f1c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation-processor/build/libs/expo-modules-core$android-annotation-processor-1.1.1.jar differ
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/resources/main/META-INF/services/com.google.devtools.ksp.processing.SymbolProcessorProvider b/node_modules/expo-modules-core/android-annotation-processor/build/resources/main/META-INF/services/com.google.devtools.ksp.processing.SymbolProcessorProvider
new file mode 100644
index 0000000..ee1a137
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation-processor/build/resources/main/META-INF/services/com.google.devtools.ksp.processing.SymbolProcessorProvider
@@ -0,0 +1 @@
+expo.modules.annotationprocessor.ExpoSymbolProcessorProvider
diff --git a/node_modules/expo-modules-core/android-annotation-processor/build/tmp/jar/MANIFEST.MF b/node_modules/expo-modules-core/android-annotation-processor/build/tmp/jar/MANIFEST.MF
new file mode 100644
index 0000000..59499bc
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation-processor/build/tmp/jar/MANIFEST.MF
@@ -0,0 +1,2 @@
+Manifest-Version: 1.0
+
diff --git a/node_modules/expo-modules-core/android-annotation/build/.transforms/04295f4a21dc29f95a439f4c89205442/results.bin b/node_modules/expo-modules-core/android-annotation/build/.transforms/04295f4a21dc29f95a439f4c89205442/results.bin
new file mode 100644
index 0000000..c8bd6f3
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation/build/.transforms/04295f4a21dc29f95a439f4c89205442/results.bin
@@ -0,0 +1 @@
+o/jetified-expo-modules-core$android-annotation-1.1.1
diff --git a/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation.kotlin_module b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation.kotlin_module
new file mode 100644
index 0000000..1e9f2ca
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/META-INF/expo-modules-core$android-annotation.kotlin_module differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/Config.class b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/Config.class
new file mode 100644
index 0000000..9d86c4d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/Config.class differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/ConverterBinder.class b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/ConverterBinder.class
new file mode 100644
index 0000000..b9a7600
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/classes/kotlin/main/expo/modules/annotation/ConverterBinder.class differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..6efd0bc
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..e800ae9
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..e3a5dcb
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..dd6dda0
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..af16c6a
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..ff24dfe
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..4f83f43
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..dca805e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fc0e221
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..c6788f6
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..e705e42
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..4f83f43
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..dca805e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..10ef01f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..c6788f6
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab
new file mode 100644
index 0000000..ce95448
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream
new file mode 100644
index 0000000..12ba709
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len
new file mode 100644
index 0000000..b01f22d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at
new file mode 100644
index 0000000..0a8129e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i
new file mode 100644
index 0000000..ac16349
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..e705e42
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..5ea5a85
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..dca805e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..10ef01f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..3b3e518
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..0925e19
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..507007d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..25c4d2f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..7d18c24
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..d642f0a
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..e081e24
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..8e4a540
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..e3a5dcb
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..c55f2cd
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..3b01c18
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream
new file mode 100644
index 0000000..2770576
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
new file mode 100644
index 0000000..c54fd0d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at
new file mode 100644
index 0000000..654846f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i
new file mode 100644
index 0000000..5117c8c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream
new file mode 100644
index 0000000..2bab446
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
new file mode 100644
index 0000000..dfa34aa
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at
new file mode 100644
index 0000000..d082e0f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i
new file mode 100644
index 0000000..a4901f3
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..2ceb12b
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+2
+0
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..5044349
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..8e4a540
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..e3a5dcb
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..7d30a43
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..730fd77
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..b996f91
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..100d205
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..ccfcbf4
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..10ef01f
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i
new file mode 100644
index 0000000..f768a77
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..646fd04
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..c3eaac3
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..1d301ab
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..b94f48d
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..157162c
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..bb89fe1
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/last-build.bin b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/last-build.bin
new file mode 100644
index 0000000..f8beffc
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/cacheable/last-build.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin
new file mode 100644
index 0000000..9c3ceb5
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/local-state/build-history.bin b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/local-state/build-history.bin
new file mode 100644
index 0000000..3774cf9
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/kotlin/compileKotlin/local-state/build-history.bin differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/libs/expo-modules-core$android-annotation-1.1.1.jar b/node_modules/expo-modules-core/android-annotation/build/libs/expo-modules-core$android-annotation-1.1.1.jar
new file mode 100644
index 0000000..235a706
Binary files /dev/null and b/node_modules/expo-modules-core/android-annotation/build/libs/expo-modules-core$android-annotation-1.1.1.jar differ
diff --git a/node_modules/expo-modules-core/android-annotation/build/tmp/jar/MANIFEST.MF b/node_modules/expo-modules-core/android-annotation/build/tmp/jar/MANIFEST.MF
new file mode 100644
index 0000000..59499bc
--- /dev/null
+++ b/node_modules/expo-modules-core/android-annotation/build/tmp/jar/MANIFEST.MF
@@ -0,0 +1,2 @@
+Manifest-Version: 1.0
+
diff --git a/node_modules/expo-modules-core/android/ExpoModulesCorePlugin.gradle b/node_modules/expo-modules-core/android/ExpoModulesCorePlugin.gradle
index e1cc7f5..ed85369 100644
--- a/node_modules/expo-modules-core/android/ExpoModulesCorePlugin.gradle
+++ b/node_modules/expo-modules-core/android/ExpoModulesCorePlugin.gradle
@@ -70,15 +70,18 @@ ext.useExpoPublishing = {
   }
   
   afterEvaluate {
-    publishing {
-      publications {
-        release(MavenPublication) {
-          from components.release
+    // Only apply publishing if the release component exists
+    if (project.components.findByName('release') != null) {
+      publishing {
+        publications {
+          release(MavenPublication) {
+            from components.release
+          }
         }
-      }
-      repositories {
-        maven {
-          url = mavenLocal().url
+        repositories {
+          maven {
+            url = mavenLocal().url
+          }
         }
       }
     }
diff --git a/node_modules/expo-modules-core/android/build.gradle b/node_modules/expo-modules-core/android/build.gradle
index ffd50ba..19d663b 100644
--- a/node_modules/expo-modules-core/android/build.gradle
+++ b/node_modules/expo-modules-core/android/build.gradle
@@ -231,6 +231,18 @@ dependencies {
   api "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0"
   api "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"
   api "androidx.core:core-ktx:1.12.0"
+  api "androidx.appcompat:appcompat:1.6.1"
+  api "androidx.fragment:fragment-ktx:1.6.2"
+  api "androidx.lifecycle:lifecycle-common:2.7.0"
+  api "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
+  api "androidx.activity:activity-ktx:1.8.2"
+  api "androidx.collection:collection-ktx:1.3.0"
+  api "androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
+  api "androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"
+  api "androidx.arch.core:core-runtime:2.2.0"
+    api "androidx.core:core:1.12.0"
+  api "androidx.lifecycle:lifecycle-process:2.7.0"
+  api "androidx.savedstate:savedstate:1.2.1"
   api project(':expo-modules-core$android-annotation')
 
   implementation("androidx.tracing:tracing-ktx:1.2.0")
diff --git a/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java
new file mode 100644
index 0000000..3ded53c
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java
@@ -0,0 +1,12 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules";
+  public static final String BUILD_TYPE = "debug";
+  // Field from default config.
+  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = false;
+}
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..223b18b
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,18 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+    <application>
+        <meta-data
+            android:name="org.unimodules.core.AppLoader#react-native-headless"
+            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
+        <meta-data
+            android:name="com.facebook.soloader.enabled"
+            android:value="true"
+            tools:replace="android:value" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..abb1565
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..913cc58
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..db36328
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Mon Jun 23 02:24:57 IST 2025
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..6288140
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..7a86b1b
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,26 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="expo.modules" >
+5
+6    <uses-sdk android:minSdkVersion="23" />
+7
+8    <application>
+8-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:4:5-12:19
+9        <meta-data
+9-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:5:9-7:89
+10            android:name="org.unimodules.core.AppLoader#react-native-headless"
+10-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:6:13-79
+11            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
+11-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:7:13-86
+12        <meta-data
+12-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:8:9-11:45
+13            android:name="com.facebook.soloader.enabled"
+13-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:9:13-57
+14            android:value="true"
+14-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:10:13-33
+15            tools:replace="android:value" />
+15-->D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:11:13-42
+16    </application>
+17
+18</manifest>
diff --git a/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..223b18b
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,18 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+    <application>
+        <meta-data
+            android:name="org.unimodules.core.AppLoader#react-native-headless"
+            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
+        <meta-data
+            android:name="com.facebook.soloader.enabled"
+            android:value="true"
+            tools:replace="android:value" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/expo-modules-core/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..eeeb776
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
new file mode 100644
index 0000000..e596ba5
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
@@ -0,0 +1,164 @@
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\CoreLogger.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\JSTypeConverterHelper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\adapters\react\apploader\HeadlessAppLoaderNotifier.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\devtools\OkHttpHeadersExtension.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\AnyTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\JSTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\OnActivityResultPayload.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\CallbacksDefinition.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ErrorView.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptWeakObject.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\Required.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\EventsDefinition.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\AsyncFunctionBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\OSLogHandler.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\AppContextActivityResultRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\UnitTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\BaseAsyncFunctionComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ReadableArrayIterator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\ByteArrayTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewManagerDefinition.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\allocators\UnsafeAllocator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\EitherTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\allocators\ObjectConstructorFactory.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ReactLifecycleDelegate.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ConcatIterator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\exception\CommonExceptions.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\LoggerOptions.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\Field.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\EventEmitter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\net\URLTypConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\modules\Module.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\ActivityResultsManager.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\devtools\cdp\CdpNetworkTypes.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\interfaces\permissions\PermissionsResponse.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\PromiseImpl.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\providers\AppContextProvider.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\Either.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\adapters\react\permissions\PermissionsService.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\ColorTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\SyncFunctionComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityaware\AppCompatActivityAware.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\KPromiseWrapper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\objects\PropertyComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\Record.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\io\FileTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JSIInteropModuleRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewGroupDefinition.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\sharedobjects\SharedObjectTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\interfaces\filesystem\AppDirectoriesModuleInterface.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\adapters\react\apploader\RNHeadlessAppLoader.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptFunction.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\exception\ExceptionDecorator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ModuleRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\GroupViewManagerWrapper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\LogHandler.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\classcomponent\ClassComponentBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\PersistentFileLog.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\exception\CodedException.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\utilities\EmulatorUtilities.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\AppContextActivityResultLauncher.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\com\facebook\react\uimanager\ReactStylesDiffMapHelper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\PersistentFileLogHandler.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\LogType.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptObject.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\ModuleRegistryDelegate.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\objects\PropertyComponentBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\defaultmodules\CoreModule.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\sharedobjects\SharedRef.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\FilteredReadableMap.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\FilteredIterator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewManagerWrapperDelegate.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\Enumerable.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\adapters\react\FabricComponentsRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\modules\DefinitionMarker.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\modules\ModuleDefinitionBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\typedarray\ConcreteTypedArrays.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\PersistentFileLogSerialDispatchQueue.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\AppContextActivityResultContract.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JNIDeallocator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptValue.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ExpoView.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewManagerType.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\EventName.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\AsyncFunctionComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\devtools\ExpoNetworkInspectOkHttpInterceptors.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\ListTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\typedarray\TypedArray.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\errors\ContextDestroyedException.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\folly\FollyDynamicExtensionConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\logging\Logger.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JNIFunctionBody.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\EnumTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\PairTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\tracing\ExpoTrace.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\AsyncFunction.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\Validators.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\TypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ModulesProvider.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\sharedobjects\SharedObjectRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\viewevent\ViewEvent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptModuleObject.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ModuleHolder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\TypeConverterProvider.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\AppContextActivityResultFallbackCallback.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\DataPersistor.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\Utils.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaCallback.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\uuidv5\Uuidv5.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\typedarray\GenericTypedArray.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\TypedArrayTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewDefinitionBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\CppType.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\allocators\ObjectConstructor.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\modules\ModuleDefinitionData.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\uuidv5\Exceptions.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityaware\OnActivityAvailableListener.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\KModuleEventEmitterWrapper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\typedarray\TypedArrayIterator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\DateTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityaware\AppCompatActivityAwareHelper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\sharedobjects\SharedObject.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\SuspendFunctionComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\utilities\KotlinUtilities.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\defaultmodules\ErrorManagerModule.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\providers\CurrentActivityProvider.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\AppContext.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\objects\ObjectDefinitionData.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\net\UriTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\Promise.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\FieldValidator.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ReadableTypeExtensions.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\RecordTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\ArrayTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\activityresult\AppContextActivityResultCaller.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\classcomponent\ClassDefinitionData.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\DynamicExtenstions.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\events\EventListener.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\AsyncFunctionWithPromiseComponent.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ConcreteViewProp.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\records\ValidationBinder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\defaultmodules\NativeModulesProxyModule.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\viewevent\ViewEventDelegate.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\ExpectedType.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\FunctionBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\apifeatures\Features.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\JavaScriptFunctionTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\jni\JavaScriptTypedArray.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\AnyType.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\AnyViewProp.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\SetTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\io\PathTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\KotlinInteropModuleRegistry.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\errors\ModuleDestroyedException.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\functions\AnyFunction.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\MapTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\types\ReadableArgumentsTypeConverter.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\core\ModulePriorities.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\devtools\ExpoRequestCdpInterceptor.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\objects\ObjectDefinitionBuilder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\ExpoModulesHelper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewWrapperDelegateHolder.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\SimpleViewManagerWrapper.kt
+D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\java\expo\modules\kotlin\views\ViewGroupDefinitionBuilder.kt
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin
new file mode 100644
index 0000000..c29e66e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin differ
diff --git a/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..129bc34
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,34 @@
+-- Merging decision tree log ---
+manifest
+ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:1-14:12
+INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:1-14:12
+	package
+		INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
+	xmlns:tools
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:2:11-57
+	xmlns:android
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:11-69
+application
+ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:4:5-12:19
+meta-data#org.unimodules.core.AppLoader#react-native-headless
+ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:5:9-7:89
+	android:value
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:7:13-86
+	android:name
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:6:13-79
+meta-data#com.facebook.soloader.enabled
+ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:8:9-11:45
+	tools:replace
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:11:13-42
+	android:value
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:10:13-33
+	android:name
+		ADDED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:9:13-57
+uses-sdk
+INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
+INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from D:\ClipBee\ClipboardManager\mobile\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/ModuleRegistryAdapter.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/ModuleRegistryAdapter.java
index bade733..f146cec 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/ModuleRegistryAdapter.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/ModuleRegistryAdapter.java
@@ -9,7 +9,7 @@ import java.util.ArrayList;
 import java.util.List;
 import java.util.Objects;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 import expo.modules.BuildConfig;
 import expo.modules.core.ModuleRegistry;
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/NativeModulesProxy.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/NativeModulesProxy.java
index 9b6db57..3eac919 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/NativeModulesProxy.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/NativeModulesProxy.java
@@ -1,5 +1,7 @@
 package expo.modules.adapters.react;
 
+import android.support.annotation.Nullable;
+
 import android.util.SparseArray;
 
 import com.facebook.react.bridge.Dynamic;
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/PromiseWrapper.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/PromiseWrapper.java
index 334761c..07439f5 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/PromiseWrapper.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/PromiseWrapper.java
@@ -1,5 +1,7 @@
 package expo.modules.adapters.react;
 
+import android.support.annotation.Nullable;
+
 import android.os.Bundle;
 
 import com.facebook.react.bridge.Arguments;
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/apploader/RNHeadlessAppLoader.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/apploader/RNHeadlessAppLoader.kt
index d2dae6a..0091b6e 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/apploader/RNHeadlessAppLoader.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/apploader/RNHeadlessAppLoader.kt
@@ -1,5 +1,9 @@
 package expo.modules.adapters.react.apploader
 
+import android.arch.lifecycle.LifecycleOwner
+
+import android.arch.lifecycle.Lifecycle
+
 import android.content.Context
 import com.facebook.react.ReactApplication
 import com.facebook.react.ReactInstanceManager
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/permissions/PermissionsService.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/permissions/PermissionsService.kt
index ae0a811..76fc7d7 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/permissions/PermissionsService.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/permissions/PermissionsService.kt
@@ -1,5 +1,12 @@
 package expo.modules.adapters.react.permissions
 
+import android.support.v4.app.ActivityCompat
+import android.support.v4.content.ContextCompat
+
+import android.support.v4.app.ActivityCompat
+import android.support.v4.content.ContextCompat
+import android.arch.lifecycle.Lifecycle
+
 import android.Manifest
 import android.content.Context
 import android.content.Intent
@@ -8,8 +15,6 @@ import android.content.pm.PackageManager
 import android.net.Uri
 import android.os.Bundle
 import android.provider.Settings
-import androidx.core.app.ActivityCompat
-import androidx.core.content.ContextCompat
 import com.facebook.react.modules.core.PermissionAwareActivity
 import com.facebook.react.modules.core.PermissionListener
 import expo.modules.interfaces.permissions.Permissions
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/services/UIManagerModuleWrapper.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/services/UIManagerModuleWrapper.java
index 12d6e08..e6df1c0 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/services/UIManagerModuleWrapper.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/adapters/react/services/UIManagerModuleWrapper.java
@@ -5,7 +5,7 @@ import android.content.Intent;
 import android.util.Log;
 import android.view.View;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 import com.facebook.react.bridge.ReactContext;
 import com.facebook.react.turbomodule.core.CallInvokerHolderImpl;
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/ModulePriorities.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/ModulePriorities.kt
index e57dcb7..6652315 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/ModulePriorities.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/ModulePriorities.kt
@@ -1,5 +1,9 @@
 package expo.modules.core
 
+import android.arch.lifecycle.LifecycleOwner
+
+import android.arch.lifecycle.Lifecycle
+
 /**
  * This class determines the order of the following handlers/listeners
  * - {@link ReactNativeHostHandler}
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ActivityEventListener.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ActivityEventListener.java
index 0a89b2d..13d5cec 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ActivityEventListener.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ActivityEventListener.java
@@ -3,7 +3,7 @@ package expo.modules.core.interfaces;
 import android.app.Activity;
 import android.content.Intent;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 public interface ActivityEventListener {
   public void onActivityResult(Activity activity, int requestCode, int resultCode, @Nullable Intent data);
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactActivityHandler.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactActivityHandler.java
index 0498384..136a600 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactActivityHandler.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactActivityHandler.java
@@ -9,7 +9,7 @@ import com.facebook.react.ReactActivityDelegate;
 import com.facebook.react.ReactNativeHost;
 import com.facebook.react.ReactRootView;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 /**
  * A handler API for modules to override default ReactActivityDelegate behaviors.
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactNativeHostHandler.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactNativeHostHandler.java
index 6fa1542..01a869c 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactNativeHostHandler.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/ReactNativeHostHandler.java
@@ -5,7 +5,7 @@ import com.facebook.react.bridge.JavaScriptContextHolder;
 import com.facebook.react.bridge.JavaScriptExecutorFactory;
 import com.facebook.react.bridge.ReactApplicationContext;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 public interface ReactNativeHostHandler {
   /**
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/services/UIManager.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/services/UIManager.java
index d98138d..04307b5 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/services/UIManager.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/core/interfaces/services/UIManager.java
@@ -2,7 +2,7 @@ package expo.modules.core.interfaces.services;
 
 import android.view.View;
 
-import androidx.annotation.Nullable;
+import android.support.annotation.Nullable;
 
 import expo.modules.core.interfaces.ActivityEventListener;
 import expo.modules.core.interfaces.LifecycleEventListener;
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/imageloader/ImageLoaderInterface.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/imageloader/ImageLoaderInterface.java
index eb30cc9..ab891ce 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/imageloader/ImageLoaderInterface.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/imageloader/ImageLoaderInterface.java
@@ -4,8 +4,8 @@ import android.graphics.Bitmap;
 
 import java.util.concurrent.Future;
 
-import androidx.annotation.NonNull;
-import androidx.annotation.Nullable;
+import android.support.annotation.NonNull;
+import android.support.annotation.Nullable;
 
 public interface ImageLoaderInterface {
   interface ResultListener {
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/permissions/Permissions.java b/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/permissions/Permissions.java
index f1f2745..e2239f9 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/permissions/Permissions.java
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/interfaces/permissions/Permissions.java
@@ -1,7 +1,7 @@
 package expo.modules.interfaces.permissions;
 
-import androidx.annotation.NonNull;
-import androidx.annotation.Nullable;
+import android.support.annotation.NonNull;
+import android.support.annotation.Nullable;
 import expo.modules.core.Promise;
 
 public interface Permissions {
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/AppContext.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/AppContext.kt
index b501a58..bfcc4b2 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/AppContext.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/AppContext.kt
@@ -1,13 +1,19 @@
 package expo.modules.kotlin
 
+import android.support.v7.app.AppCompatActivity
+import android.support.annotation.UiThread
+
+import android.support.v7.app.AppCompatActivity
+import android.support.annotation.UiThread
+import android.support.annotation.MainThread
+import android.arch.lifecycle.Lifecycle
+
 import android.app.Activity
 import android.content.Context
 import android.content.Intent
 import android.os.Handler
 import android.os.HandlerThread
 import android.view.View
-import androidx.annotation.UiThread
-import androidx.appcompat.app.AppCompatActivity
 import com.facebook.react.bridge.ReactApplicationContext
 import com.facebook.react.uimanager.UIManagerHelper
 import com.facebook.react.uimanager.UIManagerModule
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
index ad6a2ec..22a4054 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
@@ -1,5 +1,9 @@
 package expo.modules.kotlin
 
+import android.arch.lifecycle.LifecycleOwner
+
+import android.arch.lifecycle.Lifecycle
+
 import android.app.Activity
 import android.content.Intent
 import com.facebook.react.bridge.ActivityEventListener
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/Utils.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/Utils.kt
index ba9fdfe..cb59657 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/Utils.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/Utils.kt
@@ -1,5 +1,9 @@
 package expo.modules.kotlin
 
+import android.support.annotation.UiThread
+
+import android.support.annotation.MainThread
+
 import android.os.Looper
 import expo.modules.kotlin.exception.Exceptions
 
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAware.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAware.kt
index a604a0d..755af70 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAware.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAware.kt
@@ -1,7 +1,10 @@
 package expo.modules.kotlin.activityaware
 
+import android.support.v7.app.AppCompatActivity
+
+import android.support.v7.app.AppCompatActivity
+
 import android.app.Activity
-import androidx.appcompat.app.AppCompatActivity
 import expo.modules.kotlin.AppContext
 import kotlinx.coroutines.suspendCancellableCoroutine
 
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAwareHelper.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAwareHelper.kt
index 38548f9..cecd8a2 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAwareHelper.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/AppCompatActivityAwareHelper.kt
@@ -1,6 +1,10 @@
 package expo.modules.kotlin.activityaware
 
-import androidx.appcompat.app.AppCompatActivity
+import android.support.v7.app.AppCompatActivity
+
+import android.support.v7.app.AppCompatActivity
+import android.support.annotation.UiThread
+
 import java.lang.ref.WeakReference
 import java.util.concurrent.CopyOnWriteArrayList
 
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/OnActivityAvailableListener.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/OnActivityAvailableListener.kt
index 2ca96ef..ace2816 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/OnActivityAvailableListener.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityaware/OnActivityAvailableListener.kt
@@ -1,7 +1,12 @@
 package expo.modules.kotlin.activityaware
 
-import androidx.annotation.UiThread
-import androidx.appcompat.app.AppCompatActivity
+import android.support.annotation.UiThread
+
+import android.support.v7.app.AppCompatActivity
+
+import android.support.v7.app.AppCompatActivity
+import android.support.annotation.UiThread
+
 
 /**
  * Similar to [androidx.activity.contextaware.OnContextAvailableListener]
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/ActivityResultsManager.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/ActivityResultsManager.kt
index d4281c4..79b3c28 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/ActivityResultsManager.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/ActivityResultsManager.kt
@@ -1,9 +1,16 @@
 package expo.modules.kotlin.activityresult
 
+import android.support.v7.app.AppCompatActivity
+import android.arch.lifecycle.LifecycleOwner
+import android.arch.lifecycle.LifecycleEventObserver
+import android.arch.lifecycle.Lifecycle
+
+import android.support.v7.app.AppCompatActivity
+import android.arch.lifecycle.Lifecycle
+
 import android.app.Activity
 import android.content.Intent
-import androidx.appcompat.app.AppCompatActivity
-import androidx.lifecycle.Lifecycle
+import android.arch.lifecycle.Lifecycle
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.activityaware.AppCompatActivityAware
 import expo.modules.kotlin.activityaware.AppCompatActivityAwareHelper
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultCaller.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultCaller.kt
index f35ed87..4040ff3 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultCaller.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultCaller.kt
@@ -1,13 +1,15 @@
 package expo.modules.kotlin.activityresult
 
+import android.support.annotation.MainThread
+import android.arch.lifecycle.Lifecycle
+
 import android.app.Activity
-import androidx.annotation.MainThread
 import expo.modules.kotlin.AppContext
 import java.io.Serializable
 
 /**
  * This interface is directly based on [androidx.activity.result.ActivityResultCaller], but due to incompatibility
- * of ReactNative and Android's [androidx.lifecycle.Lifecycle] it needed to be adapted.
+ * of ReactNative and Android's [android.arch.lifecycle.Lifecycle] it needed to be adapted.
  * For more information how to use it read [androidx.activity.result.ActivityResultCaller] from `androidx.activity:activity:1.4.0`
  * or even better from `androidx.activity:activity-ktx:1.4.0`.
  *
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultRegistry.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultRegistry.kt
index b37adc1..7566d20 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultRegistry.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/activityresult/AppContextActivityResultRegistry.kt
@@ -1,5 +1,19 @@
 package expo.modules.kotlin.activityresult
 
+import android.support.v4.app.ActivityCompat
+import android.support.v7.app.AppCompatActivity
+import android.arch.lifecycle.LifecycleOwner
+import android.arch.lifecycle.LifecycleEventObserver
+import android.arch.lifecycle.Lifecycle
+import android.support.annotation.MainThread
+
+import android.support.v4.app.ActivityCompat
+import android.support.v7.app.AppCompatActivity
+import android.support.annotation.MainThread
+import android.arch.lifecycle.LifecycleOwner
+import android.arch.lifecycle.LifecycleEventObserver
+import android.arch.lifecycle.Lifecycle
+
 import android.annotation.SuppressLint
 import android.app.Activity
 import android.content.Context
@@ -16,12 +30,9 @@ import androidx.activity.result.IntentSenderRequest
 import androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions
 import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
 import androidx.activity.result.contract.ActivityResultContracts.StartIntentSenderForResult
-import androidx.annotation.MainThread
-import androidx.appcompat.app.AppCompatActivity
-import androidx.core.app.ActivityCompat
-import androidx.lifecycle.Lifecycle
-import androidx.lifecycle.LifecycleEventObserver
-import androidx.lifecycle.LifecycleOwner
+import android.arch.lifecycle.Lifecycle
+import android.arch.lifecycle.LifecycleEventObserver
+import android.arch.lifecycle.LifecycleOwner
 import expo.modules.core.utilities.ifNull
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.providers.CurrentActivityProvider
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/devtools/OkHttpHeadersExtension.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/devtools/OkHttpHeadersExtension.kt
index bd08748..48daeb9 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/devtools/OkHttpHeadersExtension.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/devtools/OkHttpHeadersExtension.kt
@@ -2,7 +2,10 @@
 
 package expo.modules.kotlin.devtools
 
-import androidx.collection.ArrayMap
+import android.support.v4.util.ArrayMap
+
+import android.support.v4.util.ArrayMap
+
 import okhttp3.Headers
 
 /**
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/providers/CurrentActivityProvider.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/providers/CurrentActivityProvider.kt
index bf4e99f..d9e9770 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/providers/CurrentActivityProvider.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/providers/CurrentActivityProvider.kt
@@ -1,21 +1,21 @@
 package expo.modules.kotlin.providers
 
-import com.facebook.react.ReactActivity
-import androidx.appcompat.app.AppCompatActivity
-import androidx.fragment.app.FragmentActivity
-import androidx.core.app.ComponentActivity
+import android.support.v7.app.AppCompatActivity
+
 import android.app.Activity
+import android.support.v7.app.AppCompatActivity
+import android.support.v4.app.FragmentActivity
+import com.facebook.react.bridge.ReactContext
 
-/**
- * A class that provides the accessor to the [ReactActivity]. It enables accessing
- * AndroidX/Android Jetpack features in Expo libraries coming from all subclassing chain:
- * [AppCompatActivity], [FragmentActivity], [ComponentActivity], [Activity]
-*/
 interface CurrentActivityProvider {
-  /**
-   * Returns the current [Activity] that should be an instance of  [ReactActivity],
-   * but it's been decided not to expose `react-native` symbols via `expo-module-core` public API.
-   * @returns null if the [Activity] is not yet available (eg. Application has not yet fully started)
-   */
   val currentActivity: Activity?
 }
+
+val ReactContext.currentActivity: Activity?
+  get() = currentActivity
+
+val AppCompatActivity.currentActivity: Activity?
+  get() = this
+
+val FragmentActivity.currentActivity: Activity?
+  get() = this
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
index 1b6cf64..75ead68 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
@@ -1,8 +1,11 @@
 package expo.modules.kotlin.types
 
+import android.support.annotation.RequiresApi
+
+import android.support.annotation.RequiresApi
+
 import android.graphics.Color
 import android.os.Build
-import androidx.annotation.RequiresApi
 import com.facebook.react.bridge.Dynamic
 import com.facebook.react.bridge.ReadableType
 import expo.modules.kotlin.exception.UnexpectedException
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/DateTypeConverter.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/DateTypeConverter.kt
index e46369d..241c3aa 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/DateTypeConverter.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/DateTypeConverter.kt
@@ -1,7 +1,10 @@
 package expo.modules.kotlin.types
 
+import android.support.annotation.RequiresApi
+
+import android.support.annotation.RequiresApi
+
 import android.os.Build
-import androidx.annotation.RequiresApi
 import com.facebook.react.bridge.Dynamic
 import com.facebook.react.bridge.ReadableType
 import expo.modules.kotlin.exception.UnexpectedException
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/folly/FollyDynamicExtensionConverter.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/folly/FollyDynamicExtensionConverter.kt
index 92e9b52..474ac8a 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/folly/FollyDynamicExtensionConverter.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/types/folly/FollyDynamicExtensionConverter.kt
@@ -2,7 +2,11 @@
 
 package expo.modules.kotlin.types.folly
 
-import android.util.ArrayMap
+import android.support.v4.util.ArrayMap
+
+import android.support.v4.util.ArrayMap
+
+
 import expo.modules.core.interfaces.DoNotStrip
 import expo.modules.kotlin.exception.CodedException
 
diff --git a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/views/ViewTypeConverter.kt b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/views/ViewTypeConverter.kt
index 20a89d8..0a60559 100644
--- a/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/views/ViewTypeConverter.kt
+++ b/node_modules/expo-modules-core/android/src/main/java/expo/modules/kotlin/views/ViewTypeConverter.kt
@@ -1,5 +1,9 @@
 package expo.modules.kotlin.views
 
+import android.support.annotation.RequiresApi
+
+import android.support.annotation.MainThread
+
 import android.view.View
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.Exceptions
