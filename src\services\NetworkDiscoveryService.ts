/**
 * Network Discovery Service for Clipsy Android App
 * Handles local network device discovery and IP detection
 */

import { Platform } from 'react-native';

// Platform-specific imports
let AsyncStorage: any = null;
if (Platform.OS !== 'web') {
  try {
    AsyncStorage = require('@react-native-async-storage/async-storage').default;
  } catch (error) {
    console.warn('AsyncStorage not available on this platform:', error);
  }
}

export interface DiscoveredDevice {
  id: string;
  name: string;
  ip: string;
  port: number;
  type: 'windows' | 'android' | 'web' | 'unknown';
  lastSeen: string;
  isReachable: boolean;
  services: string[];
}

export interface NetworkInfo {
  localIP: string;
  networkRange: string;
  isConnected: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
}

export interface NetworkDiscoveryCallbacks {
  onDeviceFound: (device: DiscoveredDevice) => void;
  onDeviceLost: (deviceId: string) => void;
  onNetworkChanged: (networkInfo: NetworkInfo) => void;
  onDiscoveryComplete: (devices: DiscoveredDevice[]) => void;
  onError: (error: string) => void;
}

class NetworkDiscoveryService {
  private callbacks: NetworkDiscoveryCallbacks | null = null;
  private discoveredDevices: Map<string, DiscoveredDevice> = new Map();
  private discoveryInterval: NodeJS.Timeout | null = null;
  private isDiscovering = false;
  private currentNetworkInfo: NetworkInfo | null = null;

  // Common Clipsy service ports to scan
  private readonly CLIPSY_PORTS = [8080, 8081, 8082, 8083, 8084, 8085];
  private readonly DISCOVERY_INTERVAL = 30000; // 30 seconds
  private readonly DEVICE_TIMEOUT = 60000; // 1 minute

  /**
   * Initialize network discovery service
   */
  initialize(callbacks: NetworkDiscoveryCallbacks) {
    this.callbacks = callbacks;
    this.loadCachedDevices();
    this.detectNetworkInfo();
  }

  /**
   * Start network discovery
   */
  async startDiscovery() {
    if (this.isDiscovering) {
      console.log('Discovery already running');
      return;
    }

    this.isDiscovering = true;
    console.log('Starting network discovery...');

    // Initial discovery
    await this.performDiscovery();

    // Set up periodic discovery
    this.discoveryInterval = setInterval(async () => {
      await this.performDiscovery();
    }, this.DISCOVERY_INTERVAL);

    console.log('Network discovery started');
  }

  /**
   * Stop network discovery
   */
  stopDiscovery() {
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = null;
    }

    this.isDiscovering = false;
    console.log('Network discovery stopped');
  }

  /**
   * Perform network discovery scan
   */
  private async performDiscovery() {
    try {
      console.log('Performing network discovery scan...');
      
      // Update network info
      await this.detectNetworkInfo();
      
      if (!this.currentNetworkInfo?.isConnected) {
        console.log('No network connection, skipping discovery');
        return;
      }

      // Get local IP and network range
      const localIP = this.currentNetworkInfo.localIP;
      if (!localIP) {
        console.log('Could not determine local IP, skipping discovery');
        return;
      }

      // Scan network range for Clipsy services
      await this.scanNetworkRange(localIP);

      // Clean up old devices
      this.cleanupOldDevices();

      // Notify discovery complete
      const devices = Array.from(this.discoveredDevices.values());
      this.callbacks?.onDiscoveryComplete(devices);

      // Cache discovered devices
      await this.cacheDevices();

    } catch (error) {
      console.error('Discovery error:', error);
      this.callbacks?.onError(`Discovery failed: ${error}`);
    }
  }

  /**
   * Scan network range for Clipsy services
   */
  private async scanNetworkRange(localIP: string) {
    const ipParts = localIP.split('.');
    if (ipParts.length !== 4) {
      console.error('Invalid IP format:', localIP);
      return;
    }

    const baseIP = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}`;
    const scanPromises: Promise<void>[] = [];

    // Scan common IP range (1-254)
    for (let i = 1; i <= 254; i++) {
      const targetIP = `${baseIP}.${i}`;
      
      // Skip our own IP
      if (targetIP === localIP) continue;

      // Scan each Clipsy port for this IP
      for (const port of this.CLIPSY_PORTS) {
        scanPromises.push(this.scanDevice(targetIP, port));
      }
    }

    // Execute scans with limited concurrency
    const batchSize = 20;
    for (let i = 0; i < scanPromises.length; i += batchSize) {
      const batch = scanPromises.slice(i, i + batchSize);
      await Promise.allSettled(batch);
    }
  }

  /**
   * Scan individual device for Clipsy service
   */
  private async scanDevice(ip: string, port: number): Promise<void> {
    try {
      // Use fetch with timeout to check if service is running
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

      const response = await fetch(`http://${ip}:${port}/clipsy/info`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const deviceInfo = await response.json();
        await this.handleDeviceFound(ip, port, deviceInfo);
      }
    } catch (error) {
      // Device not reachable or not a Clipsy service - this is normal
      // Only log if it's not a timeout/network error
      if (!error.name?.includes('Abort') && !error.message?.includes('fetch')) {
        console.debug(`Scan failed for ${ip}:${port}:`, error.message);
      }
    }
  }

  /**
   * Handle discovered device
   */
  private async handleDeviceFound(ip: string, port: number, deviceInfo: any) {
    const deviceId = `${ip}:${port}`;
    const device: DiscoveredDevice = {
      id: deviceId,
      name: deviceInfo.device_name || `Device at ${ip}`,
      ip,
      port,
      type: this.detectDeviceType(deviceInfo),
      lastSeen: new Date().toISOString(),
      isReachable: true,
      services: deviceInfo.services || ['clipsy']
    };

    const existingDevice = this.discoveredDevices.get(deviceId);
    
    if (!existingDevice) {
      // New device found
      this.discoveredDevices.set(deviceId, device);
      console.log(`New device discovered: ${device.name} at ${ip}:${port}`);
      this.callbacks?.onDeviceFound(device);
    } else {
      // Update existing device
      existingDevice.lastSeen = device.lastSeen;
      existingDevice.isReachable = true;
      existingDevice.name = device.name; // Update name in case it changed
    }
  }

  /**
   * Detect device type from device info
   */
  private detectDeviceType(deviceInfo: any): 'windows' | 'android' | 'web' | 'unknown' {
    const platform = deviceInfo.platform?.toLowerCase() || '';
    const userAgent = deviceInfo.user_agent?.toLowerCase() || '';

    if (platform.includes('windows') || userAgent.includes('windows')) {
      return 'windows';
    } else if (platform.includes('android') || userAgent.includes('android')) {
      return 'android';
    } else if (platform.includes('web') || userAgent.includes('mozilla')) {
      return 'web';
    }

    return 'unknown';
  }

  /**
   * Clean up devices that haven't been seen recently
   */
  private cleanupOldDevices() {
    const now = Date.now();
    const devicesToRemove: string[] = [];

    for (const [deviceId, device] of this.discoveredDevices) {
      const lastSeenTime = new Date(device.lastSeen).getTime();
      
      if (now - lastSeenTime > this.DEVICE_TIMEOUT) {
        devicesToRemove.push(deviceId);
      }
    }

    for (const deviceId of devicesToRemove) {
      this.discoveredDevices.delete(deviceId);
      console.log(`Device removed due to timeout: ${deviceId}`);
      this.callbacks?.onDeviceLost(deviceId);
    }
  }

  /**
   * Detect current network information
   */
  private async detectNetworkInfo() {
    try {
      // For React Native, we need to use a different approach to get network info
      // This is a simplified implementation - in a real app, you'd use react-native-netinfo
      
      const networkInfo: NetworkInfo = {
        localIP: await this.getLocalIP(),
        networkRange: '',
        isConnected: true, // Assume connected if we can get IP
        connectionType: 'wifi' // Default assumption
      };

      if (networkInfo.localIP) {
        const ipParts = networkInfo.localIP.split('.');
        if (ipParts.length === 4) {
          networkInfo.networkRange = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.0/24`;
        }
      }

      this.currentNetworkInfo = networkInfo;
      this.callbacks?.onNetworkChanged(networkInfo);

    } catch (error) {
      console.error('Failed to detect network info:', error);
      this.callbacks?.onError(`Network detection failed: ${error}`);
    }
  }

  /**
   * Get local IP address
   */
  private async getLocalIP(): Promise<string> {
    try {
      // Use a simple method to detect local IP
      // In a real implementation, you might use react-native-netinfo or similar
      
      // Try to connect to a known external service to determine local IP
      const response = await fetch('https://api.ipify.org?format=json', {
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      });
      
      if (response.ok) {
        const data = await response.json();
        // This gives us external IP, but for local network discovery
        // we need the local IP. This is a placeholder implementation.
        
        // For now, return a common local IP pattern
        // In a real app, you'd use proper network detection
        return '*************'; // Placeholder
      }
    } catch (error) {
      console.error('Failed to get local IP:', error);
    }

    return '*************'; // Fallback IP
  }

  /**
   * Get discovered devices
   */
  getDiscoveredDevices(): DiscoveredDevice[] {
    return Array.from(this.discoveredDevices.values());
  }

  /**
   * Get current network info
   */
  getCurrentNetworkInfo(): NetworkInfo | null {
    return this.currentNetworkInfo;
  }

  /**
   * Check if device is reachable
   */
  async checkDeviceReachability(device: DiscoveredDevice): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`http://${device.ip}:${device.port}/clipsy/ping`, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Cache discovered devices
   */
  private async cacheDevices() {
    if (Platform.OS === 'web' || !AsyncStorage) {
      return;
    }

    try {
      const devices = Array.from(this.discoveredDevices.values());
      await AsyncStorage.setItem('discovered_devices', JSON.stringify(devices));
    } catch (error) {
      console.error('Failed to cache devices:', error);
    }
  }

  /**
   * Load cached devices
   */
  private async loadCachedDevices() {
    if (Platform.OS === 'web' || !AsyncStorage) {
      return;
    }

    try {
      const cachedData = await AsyncStorage.getItem('discovered_devices');
      if (cachedData) {
        const devices: DiscoveredDevice[] = JSON.parse(cachedData);

        for (const device of devices) {
          // Mark cached devices as potentially unreachable
          device.isReachable = false;
          this.discoveredDevices.set(device.id, device);
        }

        console.log(`Loaded ${devices.length} cached devices`);
      }
    } catch (error) {
      console.error('Failed to load cached devices:', error);
    }
  }

  /**
   * Clear all discovered devices
   */
  async clearDiscoveredDevices() {
    this.discoveredDevices.clear();

    if (Platform.OS !== 'web' && AsyncStorage) {
      await AsyncStorage.removeItem('discovered_devices');
    }

    console.log('Cleared all discovered devices');
  }

  /**
   * Get discovery status
   */
  isDiscoveryActive(): boolean {
    return this.isDiscovering;
  }
}

// Export singleton instance
export const networkDiscoveryService = new NetworkDiscoveryService();
export default NetworkDiscoveryService;
