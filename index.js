// React Native entry point - traditional registration
import { AppRegistry, Platform } from 'react-native';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Polyfill for TextEncoder/TextDecoder (required for QR code generation)
if (typeof global.TextEncoder === 'undefined') {
  require('text-encoding-polyfill');
}

// Suppress specific native module warnings in development
const originalWarn = console.warn;
console.warn = (...args) => {
  const message = args.join(' ');

  // Suppress known development-only warnings
  if (
    message.includes('Native modules not available on this platform') ||
    message.includes('RNCClipboard') ||
    message.includes('TurboModuleRegistry.getEnforcing') ||
    message.includes('Clipboard module not available') ||
    message.includes('Native clipboard not available')
  ) {
    // Convert to a less alarming log message
    console.log('📱 Development Mode: Native clipboard module not available (expected in Expo Go)');
    return;
  }

  // Allow other warnings through
  originalWarn.apply(console, args);
};

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Something went wrong</Text>
          <Text style={styles.errorText}>
            The app encountered an error. Please restart the app.
          </Text>
          <Text style={styles.errorDetails}>
            {this.state.error?.message || 'Unknown error'}
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

// Safe app wrapper
const SafeApp = () => {
  try {
    const App = require('./App').default;
    return (
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    );
  } catch (error) {
    console.error('Failed to load App component:', error);
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Failed to Load App</Text>
        <Text style={styles.errorText}>
          There was an error loading the application. Please check your installation.
        </Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
    padding: 20,
  },
  errorTitle: {
    color: '#FF6B6B',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorText: {
    color: '#E0E0E0',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  errorDetails: {
    color: '#999999',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
});

// Register the main app component with error handling
AppRegistry.registerComponent('main', () => SafeApp);

console.log('index.js: App registered as "main" component with error handling');