// Mobile app Metro configuration
const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Support for local common directory
config.resolver.alias = {
  '@common': path.resolve(__dirname, './common'),
};

// Support additional file extensions
config.resolver.sourceExts.push('cjs');

// Asset extensions
config.resolver.assetExts.push('ttf');

module.exports = config;
