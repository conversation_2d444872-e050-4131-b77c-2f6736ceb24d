/**
 * QR Code Scanner Component for Clipsy Android App
 * Provides camera-based QR code scanning for device pairing
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { qrCodeService, QRCodeData } from '../services/QRCodeService';

// Platform-specific imports
let BarCodeScanner: any = null;
if (Platform.OS !== 'web') {
  try {
    BarCodeScanner = require('expo-barcode-scanner').BarCodeScanner;
  } catch (error) {
    console.warn('BarCodeScanner not available on this platform:', error);
  }
}

interface QRCodeScannerProps {
  isVisible: boolean;
  onClose: () => void;
  onScanSuccess: (data: QRCodeData) => void;
  onConnectionSuccess: () => void;
}

const QRCodeScanner: React.FC<QRCodeScannerProps> = ({
  isVisible,
  onClose,
  onScanSuccess,
  onConnectionSuccess,
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    if (isVisible) {
      requestCameraPermissions();
      initializeQRService();
    }
  }, [isVisible]);

  const requestCameraPermissions = async () => {
    if (Platform.OS === 'web') {
      setHasPermission(false);
      return;
    }

    try {
      const hasPermissions = await qrCodeService.requestCameraPermissions();
      setHasPermission(hasPermissions);
    } catch (error) {
      console.warn('Failed to request camera permissions:', error);
      setHasPermission(false);
    }
  };

  const initializeQRService = () => {
    if (Platform.OS === 'web') {
      return;
    }

    try {
      qrCodeService.initialize({
        onScanSuccess: (data: QRCodeData) => {
          console.log('QR scan successful:', data);
          setScanned(true);
          onScanSuccess(data);
        },
        onScanError: (error: string) => {
          console.error('QR scan error:', error);
          Alert.alert('Scan Error', error, [
            { text: 'Try Again', onPress: () => setScanned(false) },
            { text: 'Cancel', onPress: onClose }
          ]);
        },
        onPermissionDenied: () => {
          setHasPermission(false);
          Alert.alert(
            'Camera Permission Required',
            'Please enable camera access to scan QR codes for device pairing.',
            [{ text: 'OK', onPress: onClose }]
          );
        },
        onConnectionAttempt: (data: QRCodeData) => {
          setIsConnecting(true);
          console.log('Attempting connection to:', data.device_name);
        },
      onConnectionSuccess: () => {
        setIsConnecting(false);
        onConnectionSuccess();
        onClose();
      },
      onConnectionError: (error: string) => {
        setIsConnecting(false);
        console.error('Connection error:', error);
        setScanned(false); // Allow scanning again
      }
    });
    } catch (error) {
      console.error('Failed to initialize QR service:', error);
    }
  };

  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    if (scanned || isConnecting) return;
    
    console.log('Barcode scanned:', { type, data });
    qrCodeService.handleQRCodeScan(data);
  };

  const resetScanner = () => {
    setScanned(false);
    qrCodeService.resetScanning();
  };

  if (!isVisible) {
    return null;
  }

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      </View>
    );
  }

  if (hasPermission === false) {
    // Web platform fallback
    if (Platform.OS === 'web') {
      return (
        <View style={styles.container}>
          <StatusBar barStyle="light-content" backgroundColor="#000000" />

          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>QR Scanner</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Web Not Supported Message */}
          <View style={styles.permissionContainer}>
            <Text style={styles.permissionText}>Camera Not Available</Text>
            <Text style={[styles.permissionText, { fontSize: 14, marginBottom: 30 }]}>
              QR code scanning is not available on web browsers.{'\n'}
              Please use the mobile app to scan QR codes for device pairing.
            </Text>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Text style={styles.permissionText}>Camera access is required to scan QR codes</Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestCameraPermissions}>
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scan QR Code</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Camera View */}
      <View style={styles.cameraContainer}>
        {BarCodeScanner ? (
          <BarCodeScanner
            onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
            style={styles.camera}
            barCodeTypes={[BarCodeScanner.Constants.BarCodeType.qr]}
          />
        ) : (
          <View style={[styles.camera, { backgroundColor: '#333', alignItems: 'center', justifyContent: 'center' }]}>
            <Text style={{ color: '#fff', fontSize: 16 }}>Camera not available</Text>
          </View>
        )}
        
        {/* Scanning Overlay */}
        <View style={styles.overlay}>
          <View style={styles.scanArea}>
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
          </View>
        </View>
      </View>

      {/* Instructions */}
      <View style={styles.instructionsContainer}>
        {isConnecting ? (
          <View style={styles.connectingContainer}>
            <Text style={styles.connectingText}>Connecting to device...</Text>
            <Text style={styles.connectingSubtext}>Please wait while we establish the connection</Text>
          </View>
        ) : scanned ? (
          <View style={styles.scannedContainer}>
            <Text style={styles.scannedText}>QR Code Scanned!</Text>
            <Text style={styles.scannedSubtext}>Processing connection...</Text>
            <TouchableOpacity style={styles.scanAgainButton} onPress={resetScanner}>
              <Text style={styles.scanAgainButtonText}>Scan Again</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.instructionsTextContainer}>
            <Text style={styles.instructionsTitle}>Scan Device QR Code</Text>
            <Text style={styles.instructionsText}>
              Point your camera at the QR code displayed on the device you want to connect to.
            </Text>
            <Text style={styles.instructionsSubtext}>
              Make sure the QR code is clearly visible within the scanning area.
            </Text>
          </View>
        )}
      </View>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.cancelActionButton} onPress={onClose}>
          <Text style={styles.cancelActionButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get('window');
const scanAreaSize = width * 0.7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanArea: {
    width: scanAreaSize,
    height: scanAreaSize,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#00FF00',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  instructionsContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 30,
    minHeight: 120,
    justifyContent: 'center',
  },
  instructionsTextContainer: {
    alignItems: 'center',
  },
  instructionsTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  instructionsText: {
    color: '#CCCCCC',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 20,
  },
  instructionsSubtext: {
    color: '#999999',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  connectingContainer: {
    alignItems: 'center',
  },
  connectingText: {
    color: '#00FF00',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  connectingSubtext: {
    color: '#CCCCCC',
    fontSize: 14,
    textAlign: 'center',
  },
  scannedContainer: {
    alignItems: 'center',
  },
  scannedText: {
    color: '#00FF00',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  scannedSubtext: {
    color: '#CCCCCC',
    fontSize: 14,
    marginBottom: 15,
  },
  scanAgainButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  scanAgainButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  bottomActions: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
  },
  cancelActionButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.4)',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelActionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  permissionContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  permissionText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.4)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.4)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default QRCodeScanner;
