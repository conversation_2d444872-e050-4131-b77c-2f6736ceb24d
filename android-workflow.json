{"androidWorkflow": {"projectInfo": {"name": "Clipsy-Android", "version": "1.0.0", "platform": "Android", "framework": "React Native + Expo SDK 50", "packageName": "com.clipsy.mobile", "targetSdk": "Android API 34", "minSdk": "Android API 21", "buildSystem": "EAS Build", "repository": "https://github.com/Last0ne-1/Clipsy-Android.git"}, "architecture": {"pattern": "Service-Component Architecture", "stateManagement": "React Hooks + useState", "navigation": "Expo Router", "networking": "WebSocket + Network Discovery", "storage": "SQLite + AsyncStorage", "permissions": "Expo Permissions API"}, "coreServices": {"clipboardService": {"file": "src/services/ClipboardService.ts", "purpose": "Cross-platform clipboard management", "implementation": "expo-clipboard", "features": ["getStringAsync() - Async clipboard read", "setStringAsync() - Async clipboard write", "Platform detection (Android/Web)", "Fallback mechanisms for compatibility", "History management with SQLite", "Device-specific clipboard tracking"], "androidSpecific": {"permissions": ["SYSTEM_ALERT_WINDOW"], "backgroundTasks": "Foreground service for monitoring", "storage": "SQLite database for history"}}, "floatingOverlayService": {"file": "src/services/FloatingOverlayService.ts", "purpose": "Android floating overlay management", "implementation": "Native Android overlay simulation", "features": ["SYSTEM_ALERT_WINDOW permission handling", "Overlay visibility state management", "Cross-device clipboard item management", "Copy-to-clipboard functionality", "Mock data for PC/Server connections"], "androidSpecific": {"overlayPermission": "SYSTEM_ALERT_WINDOW", "drawOverApps": "Floating widget capability", "deviceTypes": ["android", "windows", "linux"]}}, "backgroundSyncService": {"file": "src/services/BackgroundSyncService.ts", "purpose": "Background clipboard synchronization", "implementation": "expo-task-manager + expo-background-fetch", "features": ["Background task registration", "Periodic clipboard monitoring", "Cross-device sync coordination", "Foreground service management", "Error handling and fallbacks"], "androidSpecific": {"permissions": ["FOREGROUND_SERVICE", "WAKE_LOCK"], "taskManager": "Expo TaskManager for background tasks", "syncInterval": "15 seconds minimum"}}, "networkDiscoveryService": {"file": "src/services/NetworkDiscoveryService.ts", "purpose": "Discover Windows PCs and Linux servers", "implementation": "Network scanning + mDNS", "features": ["WiFi network device discovery", "Service type detection (Clipsy protocol)", "Device capability assessment", "Connection status monitoring", "Mock device simulation"], "androidSpecific": {"permissions": ["ACCESS_WIFI_STATE", "CHANGE_WIFI_STATE"], "networkInfo": "react-native-network-info", "discovery": "Local network scanning"}}, "permissionsService": {"file": "src/services/PermissionsService.ts", "purpose": "Android permission management", "implementation": "Expo Permissions API", "features": ["Runtime permission requests", "Permission status monitoring", "Critical permission handling", "User-friendly permission dialogs", "Fallback for denied permissions"], "androidSpecific": {"permissions": ["CAMERA - QR code scanning", "SYSTEM_ALERT_WINDOW - Floating overlay", "FOREGROUND_SERVICE - Background sync", "WAKE_LOCK - Keep sync active", "ACCESS_NETWORK_STATE - Network monitoring"]}}}, "uiComponents": {"demoClipsyInterface": {"file": "src/components/DemoClipsyInterface.tsx", "purpose": "Main Android application interface", "features": ["Device clipboard sections (This Device + Connected PC/Server)", "Real-time clipboard editing with save/cancel", "Floating overlay toggle button (📋)", "Settings sidebar with device discovery", "QR code pairing for PC/Server connections", "Clipboard history with tap-to-copy", "Background sync status indicators"], "androidSpecific": {"floatingButton": "📋 Accessibility shortcut for overlay", "deviceSections": "Android device + Windows PC + Linux server", "overlayStatus": "Visual indicator when floating widget active"}}, "floatingClipboardWidget": {"file": "src/components/FloatingClipboardWidget.tsx", "purpose": "Android floating overlay widget", "features": ["Modal-based floating interface", "PC/Server clipboard content display", "Device type icons (🖥️ Windows, 🐧 Linux, 📱 Android)", "Tap-to-copy functionality", "Long press for quick copy + auto-close", "Full content preview modal", "Quick actions (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)"], "androidSpecific": {"overlaySimulation": "Modal overlay simulating draw-over-apps", "deviceIcons": "Platform-specific visual identification", "tapGestures": "Android touch interaction patterns"}}, "qrCodeScanner": {"file": "src/components/QRCodeScanner.tsx", "purpose": "QR code scanning for device pairing", "implementation": "expo-barcode-scanner", "features": ["Camera-based QR scanning", "Device pairing QR code recognition", "Connection establishment", "Error handling for scan failures"], "androidSpecific": {"permissions": ["CAMERA"], "scanner": "Android camera API via Expo"}}, "crossPlatformSyncProtocol": {"file": "src/services/CrossPlatformSyncProtocol.ts", "purpose": "Universal sync protocol for all platform combinations", "implementation": "TypeScript protocol with device capabilities", "features": ["Device platform detection (Android/Windows)", "Capability-based sync optimization", "Format conversion between platforms", "Conflict resolution strategies", "Message signing and validation", "Retry logic and error handling"], "androidSpecific": {"maxClipboardSize": "1MB for Android devices", "supportedFormats": ["text", "html", "image"], "optimizations": "Content compression and truncation"}}, "androidToAndroidSync": {"file": "src/services/AndroidToAndroidSync.ts", "purpose": "Peer-to-peer Android device clipboard synchronization", "implementation": "Direct Android-to-Android sync with optimizations", "features": ["Real-time clipboard monitoring", "Auto-sync on clipboard change", "Peer-to-peer discovery", "Content size validation (1MB limit)", "Compression for large content", "Sync statistics tracking"], "androidSpecific": {"syncInterval": "3 seconds", "monitoringInterval": "2 seconds", "maxRetries": "3 attempts", "compressionThreshold": "1KB"}}, "windowsToWindowsSync": {"file": "src/services/WindowsToWindowsSync.ts", "purpose": "Windows PC to Windows PC clipboard synchronization", "implementation": "Simulated Windows sync for Android app understanding", "features": ["Rich text format support (RTF, HTML)", "File sync capabilities", "Large content handling (10MB limit)", "Format detection and conversion", "Compression and encryption support", "Windows-specific optimizations"], "androidSpecific": {"simulation": "Mock Windows sync for Android app", "formatConversion": "RTF/HTML to plain text for Android", "sizeReduction": "Large Windows content truncated for Android"}}, "androidToWindowsSync": {"file": "src/services/AndroidToWindowsSync.ts", "purpose": "Bidirectional sync between Android devices and Windows PCs", "implementation": "Cross-platform sync with format conversion", "features": ["Bidirectional sync support", "Format conversion (Android text ↔ Windows RTF)", "Size optimization for platform limits", "Conflict detection and resolution", "Platform-specific content processing", "Sync statistics and monitoring"], "androidSpecific": {"androidToWindows": "Text to RTF conversion with metadata", "windowsToAndroid": "RTF/HTML to plain text simplification", "conflictResolution": "Latest-wins, Android-wins, Windows-wins, manual", "sizeValidation": "1MB Android limit, 10MB Windows limit"}}, "syncStatusManager": {"file": "src/services/SyncStatusManager.ts", "purpose": "Centralized sync status tracking and conflict resolution", "implementation": "Comprehensive sync coordination service", "features": ["Device status monitoring", "Sync statistics aggregation", "Conflict detection and resolution", "Event logging and history", "Overall sync health monitoring", "Force sync capabilities"], "androidSpecific": {"statusUpdateInterval": "5 seconds", "logRetention": "7 days", "maxConflictHistory": "100 conflicts", "autoConflictResolution": "Latest-wins strategy"}}, "syncDiscoveryService": {"file": "src/services/SyncDiscoveryService.ts", "purpose": "Cross-platform device discovery for sync", "implementation": "Network discovery with platform detection", "features": ["Android device discovery", "Windows PC discovery", "Device capability detection", "Connection management", "Periodic scanning", "Auto-connect support"], "androidSpecific": {"scanInterval": "10 seconds", "discoveryTimeout": "5 seconds", "maxDevices": "10 devices", "preferredPorts": "[8080, 8081, 8082, 9090]"}}}, "androidManifest": {"permissions": ["CAMERA - QR code scanning for device pairing", "INTERNET - Network communication with PCs/servers", "ACCESS_NETWORK_STATE - Monitor network connectivity", "ACCESS_WIFI_STATE - WiFi network device discovery", "CHANGE_WIFI_STATE - WiFi network management", "WAKE_LOCK - Keep background sync active", "RECEIVE_BOOT_COMPLETED - Auto-start on device boot", "FOREGROUND_SERVICE - Background clipboard monitoring", "SYSTEM_ALERT_WINDOW - Floating overlay widget", "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS - Prevent sync interruption", "SCHEDULE_EXACT_ALARM - Precise sync timing", "USE_EXACT_ALARM - Alarm scheduling"], "services": ["BackgroundSyncService - Clipboard monitoring", "NetworkDiscoveryService - Device discovery", "FloatingOverlayService - Overlay management"], "activities": ["MainActivity - Main app interface", "OverlayActivity - Floating widget management"]}, "buildConfiguration": {"easBuild": {"projectId": "2ecc55da-7cb8-4c99-b116-a89b3a82c758", "profiles": {"development": {"buildType": "apk", "purpose": "Development testing", "command": "eas build --platform android --profile development"}, "preview": {"buildType": "apk", "purpose": "Internal testing", "command": "eas build --platform android --profile preview"}, "production": {"buildType": "app-bundle", "purpose": "Google Play Store", "command": "eas build --platform android --profile production"}, "production-apk": {"buildType": "apk", "purpose": "Direct installation/sideloading", "command": "eas build --platform android --profile production-apk"}}}, "dependencies": {"expo": "~50.0.0", "expo-clipboard": "~5.0.1", "expo-background-fetch": "~11.8.0", "expo-task-manager": "~11.7.0", "expo-barcode-scanner": "^12.9.3", "expo-sqlite": "~13.4.0", "@react-native-async-storage/async-storage": "1.21.0", "react-native": "0.73.6", "react-native-device-info": "^10.14.0"}}, "workflowSteps": {"development": ["1. Clone repository: git clone https://github.com/Last0ne-1/Clipsy-Android.git", "2. Install dependencies: npm install", "3. Start Expo development server: npx expo start", "4. Test on Android device via Expo Go", "5. Debug floating overlay functionality", "6. Test PC/Server connection simulation"], "testing": ["1. Test clipboard read/write operations", "2. Verify floating overlay permission handling", "3. Test background sync service", "4. Validate QR code pairing workflow", "5. Test network discovery functionality", "6. Verify cross-device clipboard sync"], "building": ["1. Configure EAS build: eas build:configure", "2. Build APK for testing: eas build --platform android --profile preview", "3. Build AAB for Play Store: eas build --platform android --profile production", "4. Test APK installation on Android devices", "5. Verify all permissions work in production build"], "deployment": ["1. Upload AAB to Google Play Console", "2. Configure app permissions in Play Console", "3. Set up app signing and security", "4. Submit for review and publication", "5. Monitor crash reports and user feedback"]}, "androidSpecificFeatures": {"floatingOverlay": {"description": "Android draw-over-apps floating clipboard widget", "permission": "SYSTEM_ALERT_WINDOW", "implementation": "Modal-based overlay simulation", "targetDevices": "Windows PCs, Linux servers", "userExperience": "Tap 📋 button → See PC/server clipboards → Tap to copy"}, "backgroundSync": {"description": "Background clipboard monitoring and sync", "permissions": ["FOREGROUND_SERVICE", "WAKE_LOCK"], "implementation": "Expo TaskManager + BackgroundFetch", "syncInterval": "15 seconds minimum", "batteryOptimization": "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"}, "deviceDiscovery": {"description": "Discover Windows PCs and Linux servers on network", "permissions": ["ACCESS_WIFI_STATE", "CHANGE_WIFI_STATE"], "implementation": "Network scanning + mDNS simulation", "supportedDevices": ["Windows Desktop", "Linux Server", "Ubuntu Desktop"]}, "qrPairing": {"description": "QR code-based device pairing", "permission": "CAMERA", "implementation": "expo-barcode-scanner", "workflow": "Scan QR from PC/server → Establish connection → Enable sync"}}, "crossPlatformSync": {"supportedPlatforms": {"primary": "Android (This app)", "targets": ["Windows PC", "Android Device", "Linux Server"], "excluded": ["iOS", "macOS"]}, "syncTypes": {"androidToAndroid": {"description": "Peer-to-peer Android device clipboard synchronization", "implementation": "src/services/AndroidToAndroidSync.ts", "features": ["Real-time clipboard monitoring", "Auto-sync on clipboard change", "1MB size limit optimization", "Compression for large content", "Error handling and retry logic"], "workflow": "Android Device A → Network → Android Device B"}, "windowsToWindows": {"description": "Windows PC to Windows PC clipboard synchronization", "implementation": "src/services/WindowsToWindowsSync.ts", "features": ["Rich text format support (RTF, HTML)", "File sync capabilities", "10MB size limit", "Compression and encryption", "Format conversion and optimization"], "workflow": "Windows PC A → Network → Windows PC B"}, "androidToWindows": {"description": "Bidirectional sync between Android devices and Windows PCs", "implementation": "src/services/AndroidToWindowsSync.ts", "features": ["Format conversion (Android text ↔ Windows RTF)", "Size optimization for platform limits", "Conflict resolution", "Bidirectional sync support", "Platform-specific optimizations"], "workflow": "Android Device ↔ Network ↔ Windows PC"}}, "syncProtocol": {"transport": "WebSocket + HTTP", "discovery": "Network scanning + QR pairing + mDNS", "security": "Device authentication via QR codes + checksums", "dataFormat": "JSON clipboard items with metadata", "conflictResolution": "Latest-wins, source-wins, target-wins, manual"}, "clipboardFlows": {"androidToAndroid": "Android A clipboard → Network → Android B clipboard", "windowsToWindows": "Windows A clipboard → Network → Windows B clipboard", "androidToWindows": "Android clipboard → Format conversion → Windows clipboard", "windowsToAndroid": "Windows clipboard → Format simplification → Android clipboard", "floatingWidget": "Any device clipboard → Android floating widget → Tap to copy"}}, "userExperience": {"primaryWorkflow": ["1. Install Clipsy on Android device", "2. Install Clipsy on Windows PC/Linux server", "3. Pair devices via QR code scanning", "4. Enable background sync on Android", "5. Copy content on PC/server", "6. Tap 📋 button on Android to see floating widget", "7. Tap any clipboard item to copy to Android", "8. Paste in any Android app"], "accessibilityFeatures": ["📋 Floating overlay shortcut button", "Screen reader compatibility", "Large touch targets for easy tapping", "Visual feedback for all actions", "Voice announcements for clipboard operations"]}, "technicalNotes": {"expoCompatibility": "SDK 50 with managed workflow", "nativeModules": "Minimal native dependencies for Expo Go compatibility", "overlayImplementation": "Modal-based simulation of Android overlay system", "backgroundTasks": "Expo TaskManager for cross-platform background execution", "clipboardApi": "expo-clipboard for reliable cross-platform clipboard access", "networkDiscovery": "Simulated with mock data for development/testing"}}}