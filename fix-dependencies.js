// fix-dependencies.js
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Fixing dependencies...');

// Update package.json
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = require(packageJsonPath);

// Update dependencies to compatible versions
packageJson.dependencies = {
  ...packageJson.dependencies,
  'expo': '~48.0.18',
  'expo-asset': '~8.9.1',
  'expo-background-fetch': '~11.1.1',
  'expo-crypto': '~12.2.1',
  'expo-device': '~5.2.1',
  'expo-font': '~11.1.1',
  'expo-router': '~1.5.3',
  'expo-splash-screen': '~0.18.2',
  'expo-sqlite': '~11.1.1',
  'expo-status-bar': '~1.4.4',
  'expo-task-manager': '~11.1.1',
  'react': '18.2.0',
  'react-native': '0.71.8',
  'react-native-gesture-handler': '~2.9.0',
  'react-native-safe-area-context': '4.5.0',
  'react-native-screens': '~3.20.0',
  'react-native-web': '~0.18.11',
};

// Update devDependencies
packageJson.devDependencies = {
  ...packageJson.devDependencies,
  '@babel/core': '^7.20.0',
  'metro': '~0.73.10',
  'metro-config': '~0.73.10',
};

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('Updated package.json with compatible versions');

// Install dependencies
try {
  console.log('Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('Dependencies installed successfully');
} catch (error) {
  console.error('Error installing dependencies:', error);
  process.exit(1);
}

console.log('Running Expo doctor...');
try {
  execSync('npx expo-doctor', { stdio: 'inherit' });
} catch (error) {
  console.log('Expo doctor may have reported issues, but we can continue');
}

console.log('All done! You can now run your Expo app.'); 