/**
 * Cross-Platform Sync Protocol for Clipsy
 * Handles Android-to-Android, Windows-to-Windows, and Android-to-Windows sync
 */

import { Platform } from 'react-native';
import * as Clipboard from 'expo-clipboard';

export type DevicePlatform = 'android' | 'windows';
export type SyncDirection = 'bidirectional' | 'send-only' | 'receive-only';
export type SyncStatus = 'idle' | 'connecting' | 'syncing' | 'connected' | 'error' | 'disconnected';

export interface SyncDevice {
  id: string;
  name: string;
  platform: DevicePlatform;
  ip: string;
  port: number;
  version: string;
  capabilities: DeviceCapabilities;
  lastSeen: number;
  status: SyncStatus;
}

export interface DeviceCapabilities {
  supportsClipboard: boolean;
  supportsFiles: boolean;
  supportsImages: boolean;
  supportsRichText: boolean;
  supportsEncryption: boolean;
  maxClipboardSize: number;
  supportedFormats: string[];
}

export interface ClipboardSyncItem {
  id: string;
  content: string;
  format: 'text' | 'html' | 'rtf' | 'image' | 'file';
  timestamp: number;
  sourceDevice: string;
  sourcePlatform: DevicePlatform;
  size: number;
  checksum: string;
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
    priority?: 'low' | 'normal' | 'high';
  };
}

export interface SyncMessage {
  type: 'handshake' | 'clipboard-update' | 'sync-request' | 'sync-response' | 'heartbeat' | 'disconnect';
  deviceId: string;
  timestamp: number;
  data?: any;
  signature?: string;
}

export interface SyncConfiguration {
  syncDirection: SyncDirection;
  autoSync: boolean;
  syncInterval: number; // milliseconds
  maxRetries: number;
  timeout: number; // milliseconds
  encryption: boolean;
  compression: boolean;
  conflictResolution: 'latest-wins' | 'manual' | 'merge';
  allowedDevices: string[];
  blockedDevices: string[];
}

export interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastSyncTime: number;
  averageSyncTime: number;
  bytesTransferred: number;
  conflictsResolved: number;
}

export class CrossPlatformSyncProtocol {
  private config: SyncConfiguration;
  private connectedDevices: Map<string, SyncDevice> = new Map();
  private syncQueue: ClipboardSyncItem[] = [];
  private stats: SyncStats;
  private callbacks: {
    onDeviceDiscovered?: (device: SyncDevice) => void;
    onDeviceConnected?: (device: SyncDevice) => void;
    onDeviceDisconnected?: (device: SyncDevice) => void;
    onClipboardSync?: (item: ClipboardSyncItem) => void;
    onSyncError?: (error: string, device?: SyncDevice) => void;
    onSyncStats?: (stats: SyncStats) => void;
  } = {};

  constructor(config: Partial<SyncConfiguration> = {}) {
    this.config = {
      syncDirection: 'bidirectional',
      autoSync: true,
      syncInterval: 5000,
      maxRetries: 3,
      timeout: 10000,
      encryption: false,
      compression: true,
      conflictResolution: 'latest-wins',
      allowedDevices: [],
      blockedDevices: [],
      ...config
    };

    this.stats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncTime: 0,
      averageSyncTime: 0,
      bytesTransferred: 0,
      conflictsResolved: 0
    };
  }

  /**
   * Initialize the sync protocol
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    this.callbacks = callbacks;
    console.log('Cross-platform sync protocol initialized');
  }

  /**
   * Create sync message
   */
  createSyncMessage(type: SyncMessage['type'], data?: any): SyncMessage {
    return {
      type,
      deviceId: this.getCurrentDeviceId(),
      timestamp: Date.now(),
      data,
      signature: this.generateSignature(type, data)
    };
  }

  /**
   * Get current device information
   */
  getCurrentDevice(): SyncDevice {
    return {
      id: this.getCurrentDeviceId(),
      name: this.getCurrentDeviceName(),
      platform: Platform.OS === 'android' ? 'android' : 'windows',
      ip: '*************', // Would be detected dynamically
      port: 8080,
      version: '1.0.0',
      capabilities: this.getCurrentDeviceCapabilities(),
      lastSeen: Date.now(),
      status: 'connected'
    };
  }

  /**
   * Get current device capabilities
   */
  private getCurrentDeviceCapabilities(): DeviceCapabilities {
    const isAndroid = Platform.OS === 'android';
    
    return {
      supportsClipboard: true,
      supportsFiles: !isAndroid, // Android has file access limitations
      supportsImages: true,
      supportsRichText: !isAndroid, // Windows has better rich text support
      supportsEncryption: true,
      maxClipboardSize: isAndroid ? 1024 * 1024 : 10 * 1024 * 1024, // 1MB vs 10MB
      supportedFormats: isAndroid 
        ? ['text', 'html', 'image']
        : ['text', 'html', 'rtf', 'image', 'file']
    };
  }

  /**
   * Android to Android sync
   */
  async syncAndroidToAndroid(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Android to Android: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Validate Android-specific constraints
      if (clipboardItem.size > 1024 * 1024) {
        throw new Error('Clipboard item too large for Android sync (max 1MB)');
      }

      // Create sync message
      const message = this.createSyncMessage('clipboard-update', {
        item: clipboardItem,
        syncType: 'android-to-android'
      });

      // Simulate network transmission
      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Android to Android sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Windows to Windows sync
   */
  async syncWindowsToWindows(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Windows to Windows: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Windows supports larger clipboard items and rich formats
      const message = this.createSyncMessage('clipboard-update', {
        item: clipboardItem,
        syncType: 'windows-to-windows',
        richFormat: true,
        compression: this.config.compression
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Windows to Windows sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Android to Windows sync
   */
  async syncAndroidToWindows(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Android to Windows: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Convert Android clipboard format to Windows-compatible format
      const convertedItem = this.convertAndroidToWindows(clipboardItem);
      
      const message = this.createSyncMessage('clipboard-update', {
        item: convertedItem,
        syncType: 'android-to-windows',
        platformConversion: true
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Android to Windows sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Windows to Android sync
   */
  async syncWindowsToAndroid(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Windows to Android: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Convert Windows clipboard format to Android-compatible format
      const convertedItem = this.convertWindowsToAndroid(clipboardItem);
      
      const message = this.createSyncMessage('clipboard-update', {
        item: convertedItem,
        syncType: 'windows-to-android',
        platformConversion: true
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Windows to Android sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Universal sync method that determines the appropriate sync type
   */
  async syncToDevice(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    const currentPlatform = Platform.OS === 'android' ? 'android' : 'windows';
    const targetPlatform = targetDevice.platform;

    if (currentPlatform === 'android' && targetPlatform === 'android') {
      return this.syncAndroidToAndroid(targetDevice, clipboardItem);
    } else if (currentPlatform === 'windows' && targetPlatform === 'windows') {
      return this.syncWindowsToWindows(targetDevice, clipboardItem);
    } else if (currentPlatform === 'android' && targetPlatform === 'windows') {
      return this.syncAndroidToWindows(targetDevice, clipboardItem);
    } else if (currentPlatform === 'windows' && targetPlatform === 'android') {
      return this.syncWindowsToAndroid(targetDevice, clipboardItem);
    } else {
      throw new Error(`Unsupported sync combination: ${currentPlatform} to ${targetPlatform}`);
    }
  }

  /**
   * Convert Android clipboard item to Windows format
   */
  private convertAndroidToWindows(item: ClipboardSyncItem): ClipboardSyncItem {
    return {
      ...item,
      format: item.format === 'text' ? 'rtf' : item.format, // Upgrade text to RTF for Windows
      metadata: {
        ...item.metadata,
        convertedFrom: 'android',
        originalFormat: item.format
      }
    };
  }

  /**
   * Convert Windows clipboard item to Android format
   */
  private convertWindowsToAndroid(item: ClipboardSyncItem): ClipboardSyncItem {
    let convertedFormat = item.format;
    let convertedContent = item.content;

    // Simplify rich formats for Android
    if (item.format === 'rtf') {
      convertedFormat = 'text';
      convertedContent = this.stripRichTextFormatting(item.content);
    } else if (item.format === 'file') {
      convertedFormat = 'text';
      convertedContent = `File: ${item.metadata?.title || 'Unknown file'}`;
    }

    return {
      ...item,
      format: convertedFormat,
      content: convertedContent,
      size: convertedContent.length,
      metadata: {
        ...item.metadata,
        convertedFrom: 'windows',
        originalFormat: item.format
      }
    };
  }

  /**
   * Strip rich text formatting for Android compatibility
   */
  private stripRichTextFormatting(rtfContent: string): string {
    // Simple RTF to plain text conversion
    return rtfContent
      .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF commands
      .replace(/[{}]/g, '') // Remove braces
      .trim();
  }

  /**
   * Transmit message to target device
   */
  private async transmitMessage(targetDevice: SyncDevice, message: SyncMessage): Promise<void> {
    // Simulate network transmission delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    console.log(`Message transmitted to ${targetDevice.name} (${targetDevice.platform}):`, message.type);
    
    // In a real implementation, this would use WebSocket or HTTP
    // For now, we'll simulate successful transmission
  }

  /**
   * Update sync statistics
   */
  private updateStats(result: 'success' | 'failure'): void {
    this.stats.totalSyncs++;
    if (result === 'success') {
      this.stats.successfulSyncs++;
    } else {
      this.stats.failedSyncs++;
    }
    this.stats.lastSyncTime = Date.now();
    
    this.callbacks.onSyncStats?.(this.stats);
  }

  /**
   * Generate message signature for security
   */
  private generateSignature(type: string, data?: any): string {
    // Simple signature generation (in production, use proper cryptographic signing)
    const content = `${type}-${JSON.stringify(data)}-${Date.now()}`;
    return btoa(content).substring(0, 16);
  }

  /**
   * Get current device ID
   */
  private getCurrentDeviceId(): string {
    // In a real implementation, this would be a persistent device identifier
    return Platform.OS === 'android' ? 'android-device-001' : 'windows-device-001';
  }

  /**
   * Get current device name
   */
  private getCurrentDeviceName(): string {
    return Platform.OS === 'android' ? 'Android Device' : 'Windows PC';
  }

  /**
   * Get sync statistics
   */
  getSyncStats(): SyncStats {
    return { ...this.stats };
  }

  /**
   * Get connected devices
   */
  getConnectedDevices(): SyncDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  /**
   * Add device to connected devices
   */
  addConnectedDevice(device: SyncDevice): void {
    this.connectedDevices.set(device.id, device);
    this.callbacks.onDeviceConnected?.(device);
  }

  /**
   * Remove device from connected devices
   */
  removeConnectedDevice(deviceId: string): void {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      this.connectedDevices.delete(deviceId);
      this.callbacks.onDeviceDisconnected?.(device);
    }
  }

  /**
   * Update sync configuration
   */
  updateConfiguration(newConfig: Partial<SyncConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfiguration(): SyncConfiguration {
    return { ...this.config };
  }
}

// Export singleton instance
export const crossPlatformSyncProtocol = new CrossPlatformSyncProtocol();
export default CrossPlatformSyncProtocol;
