/**
 * Cross-Platform Sync Protocol for Clipsy
 * Handles Android-to-Android, Windows-to-Windows, and Android-to-Windows sync
 */

import { Platform } from 'react-native';
import * as Clipboard from 'expo-clipboard';

export type DevicePlatform = 'android' | 'windows';
export type SyncDirection = 'bidirectional' | 'send-only' | 'receive-only';
export type SyncStatus = 'idle' | 'connecting' | 'syncing' | 'connected' | 'error' | 'disconnected';

export interface SyncDevice {
  id: string;
  name: string;
  platform: DevicePlatform;
  ip: string;
  port: number;
  version: string;
  capabilities: DeviceCapabilities;
  lastSeen: number;
  status: SyncStatus;
}

export interface DeviceCapabilities {
  supportsClipboard: boolean;
  supportsFiles: boolean;
  supportsImages: boolean;
  supportsRichText: boolean;
  supportsEncryption: boolean;
  maxClipboardSize: number;
  supportedFormats: string[];
}

export interface ClipboardSyncItem {
  id: string;
  content: string;
  format: 'text' | 'html' | 'rtf' | 'image' | 'file';
  timestamp: number;
  sourceDevice: string;
  sourcePlatform: DevicePlatform;
  size: number;
  checksum: string;
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
    priority?: 'low' | 'normal' | 'high';
  };
}

export interface SyncMessage {
  type: 'handshake' | 'clipboard-update' | 'sync-request' | 'sync-response' | 'heartbeat' | 'disconnect' |
        'registration-request' | 'registration-response' | 'authentication-challenge' | 'authentication-response' |
        'pairing-request' | 'pairing-response' | 'capability-exchange' | 'security-handshake';
  deviceId: string;
  timestamp: number;
  data?: any;
  signature?: string;
  nonce?: string;
  sessionId?: string;
}

export interface DeviceRegistrationRequest {
  deviceId: string;
  deviceName: string;
  platform: DevicePlatform;
  version: string;
  capabilities: DeviceCapabilities;
  publicKey?: string;
  certificateChain?: string[];
  requestedPermissions: string[];
  securityLevel: 'basic' | 'standard' | 'high';
}

export interface DeviceRegistrationResponse {
  success: boolean;
  sessionId?: string;
  authToken?: string;
  serverPublicKey?: string;
  allowedCapabilities: DeviceCapabilities;
  securityPolicy: SecurityPolicy;
  expiresAt: number;
  message?: string;
}

export interface AuthenticationChallenge {
  challengeId: string;
  challengeType: 'token' | 'certificate' | 'biometric' | 'pin';
  challengeData: string;
  expiresAt: number;
  difficulty: number;
}

export interface AuthenticationResponse {
  challengeId: string;
  response: string;
  deviceFingerprint: string;
  timestamp: number;
}

export interface PairingRequest {
  initiatorDeviceId: string;
  targetDeviceId: string;
  pairingMethod: 'qr-code' | 'pin' | 'proximity' | 'manual';
  pairingData: string;
  requestedPermissions: string[];
  expiresAt: number;
}

export interface PairingResponse {
  success: boolean;
  pairingId?: string;
  sharedSecret?: string;
  sessionKeys?: {
    encryptionKey: string;
    signingKey: string;
  };
  message?: string;
}

export interface SecurityPolicy {
  requireEncryption: boolean;
  requireAuthentication: boolean;
  allowedOperations: string[];
  maxSessionDuration: number;
  maxClipboardSize: number;
  allowedIpRanges: string[];
  trustedDevicesOnly: boolean;
}

export interface SyncConfiguration {
  syncDirection: SyncDirection;
  autoSync: boolean;
  syncInterval: number; // milliseconds
  maxRetries: number;
  timeout: number; // milliseconds
  encryption: boolean;
  compression: boolean;
  conflictResolution: 'latest-wins' | 'manual' | 'merge';
  allowedDevices: string[];
  blockedDevices: string[];
}

export interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastSyncTime: number;
  averageSyncTime: number;
  bytesTransferred: number;
  conflictsResolved: number;
}

export class CrossPlatformSyncProtocol {
  private config: SyncConfiguration;
  private connectedDevices: Map<string, SyncDevice> = new Map();
  private registeredDevices: Map<string, DeviceRegistrationResponse> = new Map();
  private activeSessions: Map<string, { deviceId: string; expiresAt: number; authToken: string }> = new Map();
  private pendingChallenges: Map<string, AuthenticationChallenge> = new Map();
  private syncQueue: ClipboardSyncItem[] = [];
  private stats: SyncStats;
  private securityPolicy: SecurityPolicy;
  private deviceKeys: { publicKey: string; privateKey: string } | null = null;
  private callbacks: {
    onDeviceDiscovered?: (device: SyncDevice) => void;
    onDeviceConnected?: (device: SyncDevice) => void;
    onDeviceDisconnected?: (device: SyncDevice) => void;
    onClipboardSync?: (item: ClipboardSyncItem) => void;
    onSyncError?: (error: string, device?: SyncDevice) => void;
    onSyncStats?: (stats: SyncStats) => void;
    onDeviceRegistrationRequest?: (request: DeviceRegistrationRequest) => Promise<boolean>;
    onPairingRequest?: (request: PairingRequest) => Promise<boolean>;
    onAuthenticationRequired?: (challenge: AuthenticationChallenge) => Promise<string>;
  } = {};

  constructor(config: Partial<SyncConfiguration> = {}) {
    this.config = {
      syncDirection: 'bidirectional',
      autoSync: true,
      syncInterval: 5000,
      maxRetries: 3,
      timeout: 10000,
      encryption: true, // Enable encryption by default for security
      compression: true,
      conflictResolution: 'latest-wins',
      allowedDevices: [],
      blockedDevices: [],
      ...config
    };

    this.stats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncTime: 0,
      averageSyncTime: 0,
      bytesTransferred: 0,
      conflictsResolved: 0
    };

    this.securityPolicy = {
      requireEncryption: true,
      requireAuthentication: true,
      allowedOperations: ['clipboard-sync', 'device-discovery', 'heartbeat'],
      maxSessionDuration: 24 * 60 * 60 * 1000, // 24 hours
      maxClipboardSize: 10 * 1024 * 1024, // 10MB
      allowedIpRanges: ['***********/16', '10.0.0.0/8', '**********/12'],
      trustedDevicesOnly: false
    };
  }

  /**
   * Initialize the sync protocol
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    this.callbacks = callbacks;
    await this.generateDeviceKeys();
    console.log('Cross-platform sync protocol initialized with security');
  }

  /**
   * Generate device keys for encryption and signing
   */
  private async generateDeviceKeys(): Promise<void> {
    try {
      // In a real implementation, this would use proper cryptographic libraries
      // For now, we'll simulate key generation
      const keyPair = {
        publicKey: `pub_${this.getCurrentDeviceId()}_${Date.now()}`,
        privateKey: `priv_${this.getCurrentDeviceId()}_${Date.now()}`
      };

      this.deviceKeys = keyPair;
      console.log('Device keys generated successfully');
    } catch (error) {
      console.error('Failed to generate device keys:', error);
    }
  }

  /**
   * Register a new device for sync
   */
  async registerDevice(request: DeviceRegistrationRequest): Promise<DeviceRegistrationResponse> {
    try {
      console.log(`Processing device registration for: ${request.deviceName} (${request.platform})`);

      // Validate device request
      if (!this.validateRegistrationRequest(request)) {
        return {
          success: false,
          message: 'Invalid registration request',
          allowedCapabilities: this.getMinimalCapabilities(),
          securityPolicy: this.securityPolicy,
          expiresAt: 0
        };
      }

      // Check if device is blocked
      if (this.config.blockedDevices.includes(request.deviceId)) {
        return {
          success: false,
          message: 'Device is blocked',
          allowedCapabilities: this.getMinimalCapabilities(),
          securityPolicy: this.securityPolicy,
          expiresAt: 0
        };
      }

      // Request user approval if callback is available
      if (this.callbacks.onDeviceRegistrationRequest) {
        const approved = await this.callbacks.onDeviceRegistrationRequest(request);
        if (!approved) {
          return {
            success: false,
            message: 'Registration denied by user',
            allowedCapabilities: this.getMinimalCapabilities(),
            securityPolicy: this.securityPolicy,
            expiresAt: 0
          };
        }
      }

      // Generate session and auth token
      const sessionId = this.generateSessionId();
      const authToken = this.generateAuthToken(request.deviceId);
      const expiresAt = Date.now() + this.securityPolicy.maxSessionDuration;

      // Determine allowed capabilities based on security level
      const allowedCapabilities = this.determineAllowedCapabilities(request.capabilities, request.securityLevel);

      // Create registration response
      const response: DeviceRegistrationResponse = {
        success: true,
        sessionId,
        authToken,
        serverPublicKey: this.deviceKeys?.publicKey,
        allowedCapabilities,
        securityPolicy: this.securityPolicy,
        expiresAt,
        message: 'Device registered successfully'
      };

      // Store registration
      this.registeredDevices.set(request.deviceId, response);
      this.activeSessions.set(sessionId, {
        deviceId: request.deviceId,
        expiresAt,
        authToken
      });

      console.log(`Device registered successfully: ${request.deviceName}`);
      return response;

    } catch (error) {
      console.error('Device registration failed:', error);
      return {
        success: false,
        message: `Registration failed: ${error.message}`,
        allowedCapabilities: this.getMinimalCapabilities(),
        securityPolicy: this.securityPolicy,
        expiresAt: 0
      };
    }
  }

  /**
   * Authenticate a device using challenge-response
   */
  async authenticateDevice(deviceId: string, challengeType: AuthenticationChallenge['challengeType'] = 'token'): Promise<AuthenticationChallenge> {
    const challengeId = this.generateChallengeId();
    const challengeData = this.generateChallengeData(challengeType);
    const expiresAt = Date.now() + 300000; // 5 minutes

    const challenge: AuthenticationChallenge = {
      challengeId,
      challengeType,
      challengeData,
      expiresAt,
      difficulty: challengeType === 'pin' ? 4 : 1
    };

    this.pendingChallenges.set(challengeId, challenge);

    console.log(`Authentication challenge created for device: ${deviceId}`);
    return challenge;
  }

  /**
   * Verify authentication response
   */
  async verifyAuthentication(response: AuthenticationResponse): Promise<boolean> {
    const challenge = this.pendingChallenges.get(response.challengeId);

    if (!challenge) {
      console.error('Challenge not found or expired');
      return false;
    }

    if (Date.now() > challenge.expiresAt) {
      this.pendingChallenges.delete(response.challengeId);
      console.error('Challenge expired');
      return false;
    }

    // Verify the response based on challenge type
    const isValid = this.validateChallengeResponse(challenge, response);

    if (isValid) {
      this.pendingChallenges.delete(response.challengeId);
      console.log('Authentication successful');
    } else {
      console.error('Authentication failed');
    }

    return isValid;
  }

  /**
   * Initiate device pairing
   */
  async initiateDevicePairing(targetDeviceId: string, method: PairingRequest['pairingMethod'] = 'qr-code'): Promise<PairingRequest> {
    const pairingData = this.generatePairingData(method);
    const expiresAt = Date.now() + 600000; // 10 minutes

    const request: PairingRequest = {
      initiatorDeviceId: this.getCurrentDeviceId(),
      targetDeviceId,
      pairingMethod: method,
      pairingData,
      requestedPermissions: ['clipboard-sync', 'device-discovery'],
      expiresAt
    };

    console.log(`Pairing request initiated for device: ${targetDeviceId}`);
    return request;
  }

  /**
   * Create sync message
   */
  createSyncMessage(type: SyncMessage['type'], data?: any): SyncMessage {
    return {
      type,
      deviceId: this.getCurrentDeviceId(),
      timestamp: Date.now(),
      data,
      signature: this.generateSignature(type, data)
    };
  }

  /**
   * Get current device information
   */
  getCurrentDevice(): SyncDevice {
    return {
      id: this.getCurrentDeviceId(),
      name: this.getCurrentDeviceName(),
      platform: Platform.OS === 'android' ? 'android' : 'windows',
      ip: '*************', // Would be detected dynamically
      port: 8080,
      version: '1.0.0',
      capabilities: this.getCurrentDeviceCapabilities(),
      lastSeen: Date.now(),
      status: 'connected'
    };
  }

  /**
   * Get current device capabilities
   */
  private getCurrentDeviceCapabilities(): DeviceCapabilities {
    const isAndroid = Platform.OS === 'android';
    
    return {
      supportsClipboard: true,
      supportsFiles: !isAndroid, // Android has file access limitations
      supportsImages: true,
      supportsRichText: !isAndroid, // Windows has better rich text support
      supportsEncryption: true,
      maxClipboardSize: isAndroid ? 1024 * 1024 : 10 * 1024 * 1024, // 1MB vs 10MB
      supportedFormats: isAndroid 
        ? ['text', 'html', 'image']
        : ['text', 'html', 'rtf', 'image', 'file']
    };
  }

  /**
   * Android to Android sync
   */
  async syncAndroidToAndroid(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Android to Android: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Validate Android-specific constraints
      if (clipboardItem.size > 1024 * 1024) {
        throw new Error('Clipboard item too large for Android sync (max 1MB)');
      }

      // Create sync message
      const message = this.createSyncMessage('clipboard-update', {
        item: clipboardItem,
        syncType: 'android-to-android'
      });

      // Simulate network transmission
      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Android to Android sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Windows to Windows sync
   */
  async syncWindowsToWindows(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Windows to Windows: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Windows supports larger clipboard items and rich formats
      const message = this.createSyncMessage('clipboard-update', {
        item: clipboardItem,
        syncType: 'windows-to-windows',
        richFormat: true,
        compression: this.config.compression
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Windows to Windows sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Android to Windows sync
   */
  async syncAndroidToWindows(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Android to Windows: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Convert Android clipboard format to Windows-compatible format
      const convertedItem = this.convertAndroidToWindows(clipboardItem);
      
      const message = this.createSyncMessage('clipboard-update', {
        item: convertedItem,
        syncType: 'android-to-windows',
        platformConversion: true
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Android to Windows sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Windows to Android sync
   */
  async syncWindowsToAndroid(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Syncing Windows to Android: ${this.getCurrentDeviceId()} -> ${targetDevice.id}`);
      
      // Convert Windows clipboard format to Android-compatible format
      const convertedItem = this.convertWindowsToAndroid(clipboardItem);
      
      const message = this.createSyncMessage('clipboard-update', {
        item: convertedItem,
        syncType: 'windows-to-android',
        platformConversion: true
      });

      await this.transmitMessage(targetDevice, message);
      
      this.updateStats('success');
      return true;
    } catch (error) {
      console.error('Windows to Android sync failed:', error);
      this.updateStats('failure');
      this.callbacks.onSyncError?.(error.toString(), targetDevice);
      return false;
    }
  }

  /**
   * Universal sync method that determines the appropriate sync type
   */
  async syncToDevice(targetDevice: SyncDevice, clipboardItem: ClipboardSyncItem): Promise<boolean> {
    const currentPlatform = Platform.OS === 'android' ? 'android' : 'windows';
    const targetPlatform = targetDevice.platform;

    if (currentPlatform === 'android' && targetPlatform === 'android') {
      return this.syncAndroidToAndroid(targetDevice, clipboardItem);
    } else if (currentPlatform === 'windows' && targetPlatform === 'windows') {
      return this.syncWindowsToWindows(targetDevice, clipboardItem);
    } else if (currentPlatform === 'android' && targetPlatform === 'windows') {
      return this.syncAndroidToWindows(targetDevice, clipboardItem);
    } else if (currentPlatform === 'windows' && targetPlatform === 'android') {
      return this.syncWindowsToAndroid(targetDevice, clipboardItem);
    } else {
      throw new Error(`Unsupported sync combination: ${currentPlatform} to ${targetPlatform}`);
    }
  }

  /**
   * Convert Android clipboard item to Windows format
   */
  private convertAndroidToWindows(item: ClipboardSyncItem): ClipboardSyncItem {
    return {
      ...item,
      format: item.format === 'text' ? 'rtf' : item.format, // Upgrade text to RTF for Windows
      metadata: {
        ...item.metadata,
        convertedFrom: 'android',
        originalFormat: item.format
      }
    };
  }

  /**
   * Convert Windows clipboard item to Android format
   */
  private convertWindowsToAndroid(item: ClipboardSyncItem): ClipboardSyncItem {
    let convertedFormat = item.format;
    let convertedContent = item.content;

    // Simplify rich formats for Android
    if (item.format === 'rtf') {
      convertedFormat = 'text';
      convertedContent = this.stripRichTextFormatting(item.content);
    } else if (item.format === 'file') {
      convertedFormat = 'text';
      convertedContent = `File: ${item.metadata?.title || 'Unknown file'}`;
    }

    return {
      ...item,
      format: convertedFormat,
      content: convertedContent,
      size: convertedContent.length,
      metadata: {
        ...item.metadata,
        convertedFrom: 'windows',
        originalFormat: item.format
      }
    };
  }

  /**
   * Strip rich text formatting for Android compatibility
   */
  private stripRichTextFormatting(rtfContent: string): string {
    // Simple RTF to plain text conversion
    return rtfContent
      .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF commands
      .replace(/[{}]/g, '') // Remove braces
      .trim();
  }

  /**
   * Transmit message to target device
   */
  private async transmitMessage(targetDevice: SyncDevice, message: SyncMessage): Promise<void> {
    // Simulate network transmission delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    console.log(`Message transmitted to ${targetDevice.name} (${targetDevice.platform}):`, message.type);
    
    // In a real implementation, this would use WebSocket or HTTP
    // For now, we'll simulate successful transmission
  }

  /**
   * Update sync statistics
   */
  private updateStats(result: 'success' | 'failure'): void {
    this.stats.totalSyncs++;
    if (result === 'success') {
      this.stats.successfulSyncs++;
    } else {
      this.stats.failedSyncs++;
    }
    this.stats.lastSyncTime = Date.now();
    
    this.callbacks.onSyncStats?.(this.stats);
  }

  /**
   * Generate message signature for security
   */
  private generateSignature(type: string, data?: any): string {
    // Simple signature generation (in production, use proper cryptographic signing)
    const content = `${type}-${JSON.stringify(data)}-${Date.now()}`;
    return btoa(content).substring(0, 16);
  }

  /**
   * Get current device ID
   */
  private getCurrentDeviceId(): string {
    // In a real implementation, this would be a persistent device identifier
    return Platform.OS === 'android' ? 'android-device-001' : 'windows-device-001';
  }

  /**
   * Get current device name
   */
  private getCurrentDeviceName(): string {
    return Platform.OS === 'android' ? 'Android Device' : 'Windows PC';
  }

  /**
   * Get sync statistics
   */
  getSyncStats(): SyncStats {
    return { ...this.stats };
  }

  /**
   * Get connected devices
   */
  getConnectedDevices(): SyncDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  /**
   * Add device to connected devices
   */
  addConnectedDevice(device: SyncDevice): void {
    this.connectedDevices.set(device.id, device);
    this.callbacks.onDeviceConnected?.(device);
  }

  /**
   * Remove device from connected devices
   */
  removeConnectedDevice(deviceId: string): void {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      this.connectedDevices.delete(deviceId);
      this.callbacks.onDeviceDisconnected?.(device);
    }
  }

  /**
   * Update sync configuration
   */
  updateConfiguration(newConfig: Partial<SyncConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfiguration(): SyncConfiguration {
    return { ...this.config };
  }

  /**
   * Validate device registration request
   */
  private validateRegistrationRequest(request: DeviceRegistrationRequest): boolean {
    return !!(
      request.deviceId &&
      request.deviceName &&
      request.platform &&
      request.version &&
      request.capabilities &&
      request.requestedPermissions &&
      request.securityLevel
    );
  }

  /**
   * Get minimal capabilities for denied requests
   */
  private getMinimalCapabilities(): DeviceCapabilities {
    return {
      supportsClipboard: false,
      supportsFiles: false,
      supportsImages: false,
      supportsRichText: false,
      supportsEncryption: false,
      maxClipboardSize: 0,
      supportedFormats: []
    };
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate authentication token
   */
  private generateAuthToken(deviceId: string): string {
    return `token_${deviceId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate challenge ID
   */
  private generateChallengeId(): string {
    return `challenge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate challenge data based on type
   */
  private generateChallengeData(type: AuthenticationChallenge['challengeType']): string {
    switch (type) {
      case 'token':
        return Math.random().toString(36).substr(2, 16);
      case 'pin':
        return Math.floor(1000 + Math.random() * 9000).toString();
      case 'certificate':
        return `cert_challenge_${Date.now()}`;
      case 'biometric':
        return `bio_challenge_${Date.now()}`;
      default:
        return Math.random().toString(36).substr(2, 12);
    }
  }

  /**
   * Determine allowed capabilities based on security level
   */
  private determineAllowedCapabilities(requested: DeviceCapabilities, securityLevel: string): DeviceCapabilities {
    const baseCapabilities = { ...requested };

    switch (securityLevel) {
      case 'basic':
        baseCapabilities.supportsEncryption = false;
        baseCapabilities.maxClipboardSize = Math.min(baseCapabilities.maxClipboardSize, 1024 * 1024); // 1MB max
        break;
      case 'standard':
        baseCapabilities.maxClipboardSize = Math.min(baseCapabilities.maxClipboardSize, 5 * 1024 * 1024); // 5MB max
        break;
      case 'high':
        baseCapabilities.supportsEncryption = true;
        // No additional restrictions for high security
        break;
    }

    return baseCapabilities;
  }

  /**
   * Validate challenge response
   */
  private validateChallengeResponse(challenge: AuthenticationChallenge, response: AuthenticationResponse): boolean {
    switch (challenge.challengeType) {
      case 'token':
        return response.response === challenge.challengeData;
      case 'pin':
        return response.response === challenge.challengeData;
      case 'certificate':
        // In a real implementation, this would verify the certificate
        return response.response.startsWith('cert_response_');
      case 'biometric':
        // In a real implementation, this would verify biometric data
        return response.response.startsWith('bio_response_');
      default:
        return false;
    }
  }

  /**
   * Generate pairing data based on method
   */
  private generatePairingData(method: PairingRequest['pairingMethod']): string {
    switch (method) {
      case 'qr-code':
        return JSON.stringify({
          deviceId: this.getCurrentDeviceId(),
          timestamp: Date.now(),
          publicKey: this.deviceKeys?.publicKey
        });
      case 'pin':
        return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit PIN
      case 'proximity':
        return `proximity_${Date.now()}`;
      case 'manual':
        return `manual_${this.getCurrentDeviceId()}`;
      default:
        return Math.random().toString(36).substr(2, 12);
    }
  }

  /**
   * Check if device is authenticated
   */
  isDeviceAuthenticated(deviceId: string): boolean {
    return this.registeredDevices.has(deviceId);
  }

  /**
   * Get security policy
   */
  getSecurityPolicy(): SecurityPolicy {
    return { ...this.securityPolicy };
  }

  /**
   * Update security policy
   */
  updateSecurityPolicy(policy: Partial<SecurityPolicy>): void {
    this.securityPolicy = { ...this.securityPolicy, ...policy };
  }

  /**
   * Clean up expired sessions and challenges
   */
  cleanupExpiredSessions(): void {
    const now = Date.now();

    // Clean up expired sessions
    for (const [sessionId, session] of this.activeSessions) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(sessionId);
        console.log(`Expired session cleaned up: ${sessionId}`);
      }
    }

    // Clean up expired challenges
    for (const [challengeId, challenge] of this.pendingChallenges) {
      if (now > challenge.expiresAt) {
        this.pendingChallenges.delete(challengeId);
        console.log(`Expired challenge cleaned up: ${challengeId}`);
      }
    }
  }
}

// Export singleton instance
export const crossPlatformSyncProtocol = new CrossPlatformSyncProtocol();
export default CrossPlatformSyncProtocol;
