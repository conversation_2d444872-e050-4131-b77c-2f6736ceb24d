/**
 * Android to Windows Sync Service
 * Handles bidirectional clipboard synchronization between Android devices and Windows PCs
 */

import { Platform } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { ClipboardSyncItem, SyncDevice, crossPlatformSyncProtocol } from './CrossPlatformSyncProtocol';
import { syncDiscoveryService } from './SyncDiscoveryService';

export interface AndroidWindowsSyncConfiguration {
  enableBidirectionalSync: boolean;
  androidToWindowsEnabled: boolean;
  windowsToAndroidEnabled: boolean;
  maxAndroidClipboardSize: number; // bytes
  maxWindowsClipboardSize: number; // bytes
  syncInterval: number; // milliseconds
  retryAttempts: number;
  formatConversion: boolean;
  autoSyncOnChange: boolean;
  prioritySync: 'android' | 'windows' | 'latest';
  conflictResolution: 'android-wins' | 'windows-wins' | 'latest-wins' | 'manual';
}

export interface AndroidWindowsSyncStats {
  totalSyncs: number;
  androidToWindowsSyncs: number;
  windowsToAndroidSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  formatConversions: number;
  conflictsResolved: number;
  bytesTransferred: number;
  averageLatency: number;
  lastSyncTime: number;
  connectedWindowsDevices: number;
}

export class AndroidToWindowsSync {
  private config: AndroidWindowsSyncConfiguration;
  private stats: AndroidWindowsSyncStats;
  private isInitialized: boolean = false;
  private syncTimer: NodeJS.Timeout | null = null;
  private lastAndroidClipboard: string = '';
  private lastWindowsClipboard: string = '';
  private callbacks: {
    onAndroidToWindowsSync?: (targetDevice: SyncDevice, item: ClipboardSyncItem) => void;
    onWindowsToAndroidSync?: (sourceDevice: SyncDevice, item: ClipboardSyncItem) => void;
    onSyncFailure?: (direction: 'android-to-windows' | 'windows-to-android', error: string) => void;
    onFormatConversion?: (from: string, to: string, content: string) => void;
    onConflictDetected?: (androidContent: string, windowsContent: string) => void;
    onSyncSuccess?: (direction: string, device: SyncDevice) => void;
  } = {};

  constructor(config: Partial<AndroidWindowsSyncConfiguration> = {}) {
    this.config = {
      enableBidirectionalSync: true,
      androidToWindowsEnabled: true,
      windowsToAndroidEnabled: true,
      maxAndroidClipboardSize: 1024 * 1024, // 1MB
      maxWindowsClipboardSize: 10 * 1024 * 1024, // 10MB
      syncInterval: 3000, // 3 seconds
      retryAttempts: 3,
      formatConversion: true,
      autoSyncOnChange: true,
      prioritySync: 'latest',
      conflictResolution: 'latest-wins',
      ...config
    };

    this.stats = {
      totalSyncs: 0,
      androidToWindowsSyncs: 0,
      windowsToAndroidSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      formatConversions: 0,
      conflictsResolved: 0,
      bytesTransferred: 0,
      averageLatency: 0,
      lastSyncTime: 0,
      connectedWindowsDevices: 0
    };
  }

  /**
   * Initialize Android to Windows sync
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    this.callbacks = callbacks;
    this.isInitialized = true;

    // Start clipboard monitoring
    if (this.config.autoSyncOnChange) {
      this.startClipboardMonitoring();
    }

    // Start periodic sync
    this.startPeriodicSync();

    console.log('Android to Windows sync initialized');
  }

  /**
   * Sync Android clipboard to Windows devices
   */
  async syncAndroidToWindows(content?: string): Promise<boolean[]> {
    if (!this.config.androidToWindowsEnabled) {
      console.log('Android to Windows sync is disabled');
      return [];
    }

    const androidContent = content || await this.getAndroidClipboardContent();
    if (!androidContent || androidContent.trim() === '') {
      console.log('No Android clipboard content to sync');
      return [];
    }

    // Validate Android content size
    if (androidContent.length > this.config.maxAndroidClipboardSize) {
      throw new Error(`Android clipboard content too large (${androidContent.length} bytes, max ${this.config.maxAndroidClipboardSize})`);
    }

    const windowsDevices = syncDiscoveryService.getWindowsDevices()
      .filter(device => device.status === 'connected');

    if (windowsDevices.length === 0) {
      console.log('No connected Windows devices found');
      return [];
    }

    const results: boolean[] = [];
    console.log(`Syncing Android content to ${windowsDevices.length} Windows devices...`);

    for (const device of windowsDevices) {
      try {
        const success = await this.performAndroidToWindowsSync(device, androidContent);
        results.push(success);
        
        if (success) {
          this.callbacks.onAndroidToWindowsSync?.(device, this.createSyncItem(androidContent, 'android'));
          this.callbacks.onSyncSuccess?.('android-to-windows', device);
        }
      } catch (error) {
        console.error(`Failed to sync Android to ${device.name}:`, error);
        results.push(false);
        this.callbacks.onSyncFailure?.('android-to-windows', error.toString());
      }
    }

    return results;
  }

  /**
   * Handle Windows to Android sync
   */
  async handleWindowsToAndroidSync(sourceDevice: SyncDevice, windowsContent: string): Promise<boolean> {
    if (!this.config.windowsToAndroidEnabled) {
      console.log('Windows to Android sync is disabled');
      return false;
    }

    try {
      console.log(`Receiving Windows content from ${sourceDevice.name}...`);

      // Convert Windows content to Android-compatible format
      const convertedContent = this.convertWindowsToAndroid(windowsContent);

      // Check for conflicts
      const currentAndroidContent = await this.getAndroidClipboardContent();
      if (currentAndroidContent && currentAndroidContent !== convertedContent) {
        await this.handleSyncConflict(currentAndroidContent, convertedContent);
      }

      // Update Android clipboard
      await Clipboard.setStringAsync(convertedContent);
      this.lastAndroidClipboard = convertedContent;

      // Update stats
      this.updateStats('windows-to-android', convertedContent.length, true);

      // Notify callbacks
      const syncItem = this.createSyncItem(convertedContent, 'windows');
      this.callbacks.onWindowsToAndroidSync?.(sourceDevice, syncItem);
      this.callbacks.onSyncSuccess?.('windows-to-android', sourceDevice);

      console.log(`Successfully synced Windows content to Android from ${sourceDevice.name}`);
      return true;

    } catch (error) {
      console.error(`Failed to handle Windows to Android sync from ${sourceDevice.name}:`, error);
      this.updateStats('windows-to-android', 0, false);
      this.callbacks.onSyncFailure?.('windows-to-android', error.toString());
      return false;
    }
  }

  /**
   * Perform Android to Windows sync
   */
  private async performAndroidToWindowsSync(targetDevice: SyncDevice, androidContent: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      // Convert Android content to Windows-compatible format
      const convertedContent = this.convertAndroidToWindows(androidContent, targetDevice);

      // Validate Windows content size
      if (convertedContent.length > targetDevice.capabilities.maxClipboardSize) {
        throw new Error(`Converted content too large for Windows device (${convertedContent.length} > ${targetDevice.capabilities.maxClipboardSize})`);
      }

      // Create sync item
      const syncItem = this.createSyncItem(convertedContent, 'android');

      // Use cross-platform protocol
      const success = await crossPlatformSyncProtocol.syncAndroidToWindows(targetDevice, syncItem);

      if (success) {
        this.updateStats('android-to-windows', convertedContent.length, true, Date.now() - startTime);
        console.log(`Successfully synced Android content to ${targetDevice.name}`);
      } else {
        this.updateStats('android-to-windows', 0, false, Date.now() - startTime);
      }

      return success;

    } catch (error) {
      this.updateStats('android-to-windows', 0, false, Date.now() - startTime);
      throw error;
    }
  }

  /**
   * Convert Android content to Windows format
   */
  private convertAndroidToWindows(androidContent: string, targetDevice: SyncDevice): string {
    if (!this.config.formatConversion) {
      return androidContent;
    }

    let convertedContent = androidContent;

    // Convert line endings to Windows format
    convertedContent = convertedContent.replace(/\n/g, '\r\n');

    // Enhance plain text for Windows rich text capabilities
    if (targetDevice.capabilities.supportsRichText) {
      // Convert URLs to clickable links (simulate RTF enhancement)
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      if (urlRegex.test(convertedContent)) {
        convertedContent = convertedContent.replace(urlRegex, '[LINK]$1[/LINK]');
        this.callbacks.onFormatConversion?.('text', 'rtf', convertedContent);
        this.stats.formatConversions++;
      }
    }

    // Add Windows-specific metadata
    convertedContent = `[ANDROID-SYNC:${Date.now()}]${convertedContent}`;

    console.log(`Converted Android content for Windows (${androidContent.length} -> ${convertedContent.length} bytes)`);
    return convertedContent;
  }

  /**
   * Convert Windows content to Android format
   */
  private convertWindowsToAndroid(windowsContent: string): string {
    if (!this.config.formatConversion) {
      return windowsContent;
    }

    let convertedContent = windowsContent;

    // Remove Windows-specific metadata
    convertedContent = convertedContent.replace(/\[ANDROID-SYNC:\d+\]/g, '');

    // Convert Windows line endings to Unix format
    convertedContent = convertedContent.replace(/\r\n/g, '\n');

    // Simplify rich text formatting for Android
    convertedContent = convertedContent.replace(/\[LINK\](.*?)\[\/LINK\]/g, '$1');

    // Remove RTF formatting if present
    if (convertedContent.includes('{\\rtf')) {
      convertedContent = convertedContent.replace(/\\[a-z]+\d*\s?/g, '').replace(/[{}]/g, '').trim();
      this.callbacks.onFormatConversion?.('rtf', 'text', convertedContent);
      this.stats.formatConversions++;
    }

    // Truncate if too large for Android
    if (convertedContent.length > this.config.maxAndroidClipboardSize) {
      convertedContent = convertedContent.substring(0, this.config.maxAndroidClipboardSize - 100) + '... [truncated for Android]';
    }

    console.log(`Converted Windows content for Android (${windowsContent.length} -> ${convertedContent.length} bytes)`);
    return convertedContent;
  }

  /**
   * Handle sync conflicts
   */
  private async handleSyncConflict(androidContent: string, windowsContent: string): Promise<void> {
    console.log('Sync conflict detected between Android and Windows content');
    
    this.callbacks.onConflictDetected?.(androidContent, windowsContent);
    this.stats.conflictsResolved++;

    switch (this.config.conflictResolution) {
      case 'android-wins':
        console.log('Conflict resolution: Android content wins');
        break;
      case 'windows-wins':
        console.log('Conflict resolution: Windows content wins');
        break;
      case 'latest-wins':
        console.log('Conflict resolution: Latest content wins');
        break;
      case 'manual':
        console.log('Conflict resolution: Manual intervention required');
        break;
    }
  }

  /**
   * Start clipboard monitoring
   */
  private startClipboardMonitoring(): void {
    const monitorClipboard = async () => {
      try {
        if (Platform.OS === 'android') {
          const currentContent = await this.getAndroidClipboardContent();
          
          if (currentContent !== this.lastAndroidClipboard && currentContent.trim() !== '') {
            console.log('Android clipboard changed, triggering sync to Windows...');
            this.lastAndroidClipboard = currentContent;
            await this.syncAndroidToWindows(currentContent);
          }
        }
      } catch (error) {
        console.error('Clipboard monitoring error:', error);
      }
    };

    setInterval(monitorClipboard, 2000);
    console.log('Android-Windows clipboard monitoring started');
  }

  /**
   * Start periodic sync
   */
  private startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      try {
        if (this.config.enableBidirectionalSync) {
          await this.syncAndroidToWindows();
        }
      } catch (error) {
        console.error('Periodic Android-Windows sync error:', error);
      }
    }, this.config.syncInterval);

    console.log(`Periodic Android-Windows sync started (interval: ${this.config.syncInterval}ms)`);
  }

  /**
   * Get Android clipboard content
   */
  private async getAndroidClipboardContent(): Promise<string> {
    try {
      return await Clipboard.getStringAsync();
    } catch (error) {
      console.error('Failed to get Android clipboard content:', error);
      return '';
    }
  }

  /**
   * Create sync item
   */
  private createSyncItem(content: string, sourcePlatform: 'android' | 'windows'): ClipboardSyncItem {
    return {
      id: `${sourcePlatform}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      format: 'text',
      timestamp: Date.now(),
      sourceDevice: `current-${sourcePlatform}-device`,
      sourcePlatform: sourcePlatform,
      size: content.length,
      checksum: this.generateChecksum(content),
      metadata: {
        title: `${sourcePlatform.charAt(0).toUpperCase() + sourcePlatform.slice(1)} Clipboard Content`,
        description: `Cross-platform sync from ${sourcePlatform}`,
        priority: 'normal',
        crossPlatform: true,
        originalPlatform: sourcePlatform
      }
    };
  }

  /**
   * Generate checksum
   */
  private generateChecksum(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Update sync statistics
   */
  private updateStats(direction: 'android-to-windows' | 'windows-to-android', bytes: number, success: boolean, latency?: number): void {
    this.stats.totalSyncs++;
    
    if (direction === 'android-to-windows') {
      this.stats.androidToWindowsSyncs++;
    } else {
      this.stats.windowsToAndroidSyncs++;
    }
    
    if (success) {
      this.stats.successfulSyncs++;
      this.stats.bytesTransferred += bytes;
      
      if (latency) {
        const totalLatency = this.stats.averageLatency * (this.stats.successfulSyncs - 1) + latency;
        this.stats.averageLatency = totalLatency / this.stats.successfulSyncs;
      }
    } else {
      this.stats.failedSyncs++;
    }
    
    this.stats.lastSyncTime = Date.now();
    this.stats.connectedWindowsDevices = syncDiscoveryService.getWindowsDevices()
      .filter(device => device.status === 'connected').length;
  }

  /**
   * Get sync statistics
   */
  getSyncStats(): AndroidWindowsSyncStats {
    return { ...this.stats };
  }

  /**
   * Get connected Windows devices
   */
  getConnectedWindowsDevices(): SyncDevice[] {
    return syncDiscoveryService.getWindowsDevices()
      .filter(device => device.status === 'connected');
  }

  /**
   * Update configuration
   */
  updateConfiguration(newConfig: Partial<AndroidWindowsSyncConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.syncInterval !== undefined) {
      this.startPeriodicSync();
    }
  }

  /**
   * Force sync now
   */
  async forceSyncNow(): Promise<boolean[]> {
    console.log('Force syncing Android to Windows devices...');
    return await this.syncAndroidToWindows();
  }

  /**
   * Stop Android to Windows sync
   */
  async stop(): Promise<void> {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    
    this.isInitialized = false;
    console.log('Android to Windows sync stopped');
  }
}

// Export singleton instance
export const androidToWindowsSync = new AndroidToWindowsSync();
export default AndroidToWindowsSync;
