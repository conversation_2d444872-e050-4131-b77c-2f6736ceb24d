// This script starts Expo with the correct configuration for <PERSON><PERSON><PERSON><PERSON>
const { exec } = require('child_process');
const path = require('path');

console.log('Starting ClipBee on Expo Go...');
console.log('Make sure your Android device has Expo Go installed');
console.log('and is connected to the same network as your computer.\n');

// Set the environment variable for TypeScript setup
process.env.EXPO_NO_TYPESCRIPT_SETUP = '1';

// Run the exact command that worked previously for this project
const command = 'npx expo-cli start --no-dev';

const child = exec(command, {
  cwd: process.cwd(),
  env: process.env
});

child.stdout.on('data', (data) => {
  console.log(data);
});

child.stderr.on('data', (data) => {
  console.error(data);
});

child.on('exit', (code) => {
  console.log(`Expo exited with code ${code}`);
});
