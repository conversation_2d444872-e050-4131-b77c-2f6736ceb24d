{
  "project": "Clipsy-Android",
  "description": "Cross-platform clipboard synchronization app for Android and Web",
  "version": "1.2.0",
  "lastUpdated": "2025-06-23",
  "
": {
    "framework": "React Native with Expo",
    "platforms": ["Android", "Web"],
    "buildSystem": "EAS Build",
    "packageManager": "npm",
    "language": "TypeScript"
  },
  "criticalIssues": {
    "clipboardCopyError": {
      "issue": "Failed to copy text error when tapping copy on device clipboard content",
      "cause": "Missing error handling and validation in clipboard operations",
      "solution": "Enhanced error handling with specific error messages and platform checks",
      "files": [
        "src/components/DemoClipsyInterface.tsx",
        "services/ClipboardService.ts"
      ],
      "fixes": [
        "Added null checks for Clipboard service availability",
        "Enhanced error messages for different failure scenarios",
        "Added content validation before copy operations",
        "Improved web platform fallback handling"
      ]
    },
    "expoPermissionsDeprecated": {
      "issue": "expo-permissions causing Kotlin plugin errors during build",
      "cause": "expo-permissions is deprecated and conflicts with modern Expo SDK",
      "solution": "Replaced with modern expo-notifications and individual permission APIs",
      "files": [
        "src/services/PermissionsService.ts",
        "package.json"
      ],
      "fixes": [
        "Removed expo-permissions dependency",
        "Updated to use expo-notifications for notification permissions",
        "Updated permission handling to use modern APIs"
      ]
    }
  },
  "buildConfiguration": {
    "easProject": "@ksvivek/clipsy-mobile",
    "projectId": "2ecc55da-7cb8-4c99-b116-a89b3a82c758",
    "packageName": "com.clipsy.mobile",
    "buildProfiles": {
      "development": {
        "output": "APK",
        "purpose": "Testing and debugging",
        "command": "eas build --platform android --profile development"
      },
      "preview": {
        "output": "APK", 
        "purpose": "Internal testing",
        "command": "eas build --platform android --profile preview"
      },
      "production": {
        "output": "AAB",
        "purpose": "Google Play Store",
        "command": "eas build --platform android --profile production"
      },
      "production-apk": {
        "output": "APK",
        "purpose": "Direct installation/sideloading",
        "command": "eas build --platform android --profile production-apk"
      }
    }
  },
  "dependencies": {
    "core": [
      "@react-native-clipboard/clipboard",
      "@react-native-community/netinfo",
      "@react-native-async-storage/async-storage",
      "expo-barcode-scanner",
      "expo-background-fetch",
      "expo-notifications",
      "expo-task-manager",
      "expo-sqlite"
    ],
    "removed": [
      "expo-permissions"
    ],
    "reasons": {
      "expo-permissions": "Deprecated and causes Kotlin plugin conflicts"
    }
  },
  "services": {
    "ClipboardService": {
      "purpose": "Handles clipboard operations and monitoring",
      "features": ["Cross-platform clipboard access", "Real-time monitoring", "History management"],
      "errorHandling": "Enhanced with specific error messages and platform checks"
    },
    "PermissionsService": {
      "purpose": "Manages app permissions",
      "features": ["Camera permissions", "Notification permissions", "Background permissions"],
      "modernization": "Updated to use expo-notifications instead of expo-permissions"
    },
    "NetworkDiscoveryService": {
      "purpose": "Discovers devices on local network",
      "features": ["Device discovery", "Connection management", "Network monitoring"]
    },
    "WebSocketService": {
      "purpose": "Real-time communication between devices",
      "features": ["Bidirectional sync", "Connection management", "Message handling"]
    }
  },
  "commonIssues": {
    "gradleErrors": {
      "solution": "Use 'npx expo prebuild --platform android' to regenerate clean Android directory"
    },
    "packageNameMismatch": {
      "solution": "Ensure app.json and build.gradle have consistent package names"
    },
    "clipboardPermissions": {
      "solution": "Check platform availability and provide fallbacks for web"
    },
    "buildFailures": {
      "solution": "Remove custom scripts from android directory and use clean prebuild"
    }
  },
  "buildCommands": {
    "setup": [
      "npm install -g eas-cli",
      "eas login",
      "eas init"
    ],
    "development": [
      "npx expo prebuild --platform android",
      "eas build --platform android --profile preview"
    ],
    "production": [
      "eas build --platform android --profile production",
      "eas submit --platform android"
    ]
  },
  "troubleshooting": {
    "clipboardErrors": [
      "Check if Clipboard service is available",
      "Verify platform-specific implementations",
      "Ensure proper error handling",
      "Test on different platforms"
    ],
    "buildErrors": [
      "Clean android directory with 'Remove-Item -Recurse -Force android'",
      "Regenerate with 'npx expo prebuild --platform android'",
      "Check for deprecated dependencies",
      "Verify package name consistency"
    ]
  },
  "platformSpecific": {
    "android": {
      "packageName": "com.clipsy.mobile",
      "permissions": [
        "CAMERA",
        "INTERNET",
        "ACCESS_NETWORK_STATE",
        "ACCESS_WIFI_STATE",
        "CHANGE_WIFI_STATE",
        "WAKE_LOCK",
        "RECEIVE_BOOT_COMPLETED",
        "FOREGROUND_SERVICE",
        "SYSTEM_ALERT_WINDOW",
        "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",
        "SCHEDULE_EXACT_ALARM",
        "USE_EXACT_ALARM"
      ],
      "buildOutputs": {
        "apk": "For testing and sideloading",
        "aab": "For Google Play Store submission"
      }
    },
    "web": {
      "clipboardFallback": "Uses document.execCommand('copy') when navigator.clipboard unavailable",
      "limitations": "Requires user interaction for clipboard access"
    }
  },
  "futureUpdates": {
    "recommendations": [
      "Monitor expo-permissions removal impact",
      "Test clipboard functionality across all platforms",
      "Implement better error recovery mechanisms",
      "Add clipboard permission status indicators",
      "Consider implementing clipboard history encryption"
    ],
    "knownLimitations": [
      "Web clipboard requires user interaction",
      "Background clipboard monitoring limited on iOS",
      "Network discovery may not work on all network configurations"
    ]
  }
}
