import React, { useEffect, useState } from 'react';
import { Text, ActivityIndicator, View, StyleSheet } from 'react-native';
import { loadIcon } from '../utils/iconLoader';

interface OptimizedIconProps {
  name: string;
  size?: number;
  color?: string;
  fallbackText?: string;
}

/**
 * OptimizedIcon Component
 * 
 * A drop-in replacement for MaterialIcons that implements
 * code splitting and lazy loading to reduce bundle size.
 */
const OptimizedIcon: React.FC<OptimizedIconProps> = ({
  name,
  size = 24,
  color = '#E0E0E0',
  fallbackText,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [iconComponent, setIconComponent] = useState<any>(null);

  useEffect(() => {
    let mounted = true;

    const loadIconAsync = async () => {
      try {
        const icon = await loadIcon(name);
        
        if (mounted && icon) {
          setIconComponent(icon);
          setLoading(false);
        } else if (mounted) {
          setError(true);
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          console.error('Failed to load icon:', err);
          setError(true);
          setLoading(false);
        }
      }
    };

    loadIconAsync();

    return () => {
      mounted = false;
    };
  }, [name]);

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="small" color={color} />
      </View>
    );
  }

  if (error || !iconComponent) {
    // Fallback to emoji or text if icon fails to load
    return (
      <Text style={[styles.fallbackText, { fontSize: size, color }]}>
        {fallbackText || '⚙️'}
      </Text>
    );
  }

  const { component: IconComponent, name: iconName } = iconComponent;
  return <IconComponent name={iconName} size={size} color={color} />;
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 24,
    height: 24,
  },
  fallbackText: {
    textAlign: 'center',
  },
});

export default OptimizedIcon;
