{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "clipsy-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#201c1c"}, "assetBundlePatterns": ["**/*"], "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#201c1c"}, "package": "com.clipsy.mobile", "permissions": ["CAMERA", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "CHANGE_WIFI_STATE", "WAKE_LOCK", "RECEIVE_BOOT_COMPLETED", "FOREGROUND_SERVICE", "SYSTEM_ALERT_WINDOW", "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS", "SCHEDULE_EXACT_ALARM", "USE_EXACT_ALARM", "android.permission.CAMERA", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"]}, "permissions": ["CAMERA", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "CHANGE_WIFI_STATE", "WAKE_LOCK", "RECEIVE_BOOT_COMPLETED", "FOREGROUND_SERVICE", "SYSTEM_ALERT_WINDOW", "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS", "SCHEDULE_EXACT_ALARM", "USE_EXACT_ALARM"], "scheme": "clipsy", "plugins": [["expo-barcode-scanner", {"cameraPermission": "Allow C<PERSON><PERSON> to access camera for QR code scanning."}], ["expo-background-fetch", {"backgroundFetchPermission": "Allow <PERSON><PERSON><PERSON> to sync clipboard in background."}], ["expo-task-manager"]], "extra": {"eas": {"projectId": "2ecc55da-7cb8-4c99-b116-a89b3a82c758"}}, "owner": "ksvivek"}}