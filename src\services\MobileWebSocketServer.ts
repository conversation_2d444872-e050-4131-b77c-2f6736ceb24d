import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface ConnectionInfo {
  ip: string;
  port: number;
  token: string;
  device_id: string;
  device_name: string;
  device_type: string;
}

export interface PairingRequest {
  device_id: string;
  device_name: string;
  token: string;
}

export interface PairingResponse {
  success: boolean;
  message: string;
  server_device_id: string;
}

export interface ClipboardMessage {
  content: string;
  timestamp: number;
  device_id: string;
  device_name: string;
  device_type: string;
  content_type: string;
}

export interface ConnectedDevice {
  device_id: string;
  device_name: string;
  device_type: string;
  connected_at: number;
  last_seen: number;
  current_clipboard?: string;
}

export interface WebSocketMessage {
  type: 'Ping' | 'Pong' | 'PairingRequest' | 'PairingResponse' | 'ClipboardSync' | 'DeviceStatus';
  [key: string]: any;
}

export type ServerStatus = 'stopped' | 'starting' | 'running' | 'error';

export interface MobileWebSocketServerCallbacks {
  onStatusChange?: (status: ServerStatus) => void;
  onDeviceConnected?: (device: ConnectedDevice) => void;
  onDeviceDisconnected?: (deviceId: string) => void;
  onClipboardReceived?: (clipboard: ClipboardMessage) => void;
  onError?: (error: string) => void;
}

class MobileWebSocketServer {
  private server: any = null; // WebSocket server instance (would be native implementation)
  private status: ServerStatus = 'stopped';
  private port: number = 8080;
  private deviceId: string = '';
  private deviceName: string = '';
  private deviceType: string = 'android';
  private pairingToken: string = '';
  private connectedDevices: Map<string, ConnectedDevice> = new Map();
  private callbacks: MobileWebSocketServerCallbacks = {};

  constructor() {
    this.generateDeviceInfo();
  }

  private async generateDeviceInfo() {
    try {
      let deviceId = await AsyncStorage.getItem('mobile_server_device_id');
      if (!deviceId) {
        deviceId = `android_server_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('mobile_server_device_id', deviceId);
      }
      this.deviceId = deviceId;
      this.deviceName = Platform.OS === 'android' ? 'Android Phone' : 'Mobile Device';
    } catch (error) {
      console.error('Failed to generate device info:', error);
      this.deviceId = `android_server_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }

  private generatePairingToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  public setCallbacks(callbacks: MobileWebSocketServerCallbacks) {
    this.callbacks = callbacks;
  }

  public getStatus(): ServerStatus {
    return this.status;
  }

  public getConnectionInfo(): ConnectionInfo | null {
    if (this.status !== 'running') {
      return null;
    }

    return {
      ip: this.getLocalIP(), // Would need native implementation to get actual IP
      port: this.port,
      token: this.pairingToken,
      device_id: this.deviceId,
      device_name: this.deviceName,
      device_type: this.deviceType,
    };
  }

  private getLocalIP(): string {
    // In a real implementation, this would get the actual local IP
    // For now, return a placeholder
    return '*************'; // Placeholder - would need native module
  }

  public async startServer(port: number = 8080): Promise<boolean> {
    if (this.status === 'running') {
      return true;
    }

    this.setStatus('starting');
    this.port = port;
    this.pairingToken = this.generatePairingToken();

    try {
      // In a real React Native app, this would use a native WebSocket server
      // For now, we'll simulate the server functionality
      console.log('Starting mobile WebSocket server on port:', port);
      
      // Simulate server startup
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.setStatus('running');
      console.log('Mobile WebSocket server started successfully');
      
      return true;
    } catch (error) {
      console.error('Failed to start mobile WebSocket server:', error);
      this.setStatus('error');
      this.callbacks.onError?.('Failed to start server');
      return false;
    }
  }

  public async stopServer(): Promise<void> {
    if (this.status === 'stopped') {
      return;
    }

    try {
      console.log('Stopping mobile WebSocket server');
      
      // Disconnect all devices
      for (const deviceId of this.connectedDevices.keys()) {
        this.handleDeviceDisconnected(deviceId);
      }
      
      // Stop server
      if (this.server) {
        // Would close native server here
        this.server = null;
      }
      
      this.setStatus('stopped');
      console.log('Mobile WebSocket server stopped');
    } catch (error) {
      console.error('Error stopping server:', error);
      this.setStatus('error');
    }
  }

  private setStatus(status: ServerStatus) {
    if (this.status !== status) {
      this.status = status;
      this.callbacks.onStatusChange?.(status);
    }
  }

  private handleDeviceConnected(device: ConnectedDevice) {
    this.connectedDevices.set(device.device_id, device);
    this.callbacks.onDeviceConnected?.(device);
    console.log('Device connected:', device.device_name);
  }

  private handleDeviceDisconnected(deviceId: string) {
    if (this.connectedDevices.has(deviceId)) {
      this.connectedDevices.delete(deviceId);
      this.callbacks.onDeviceDisconnected?.(deviceId);
      console.log('Device disconnected:', deviceId);
    }
  }

  public getConnectedDevices(): ConnectedDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  public async broadcastClipboard(content: string, contentType: string = 'text'): Promise<void> {
    if (this.status !== 'running') {
      console.warn('Cannot broadcast - server not running');
      return;
    }

    const clipboardMsg: ClipboardMessage = {
      content,
      timestamp: Date.now(),
      device_id: this.deviceId,
      device_name: this.deviceName,
      device_type: this.deviceType,
      content_type: contentType,
    };

    console.log('Broadcasting clipboard to', this.connectedDevices.size, 'devices');
    
    // In a real implementation, this would send to all connected WebSocket clients
    // For now, just log the broadcast
    console.log('Broadcast message:', clipboardMsg);
  }

  // Simulate receiving a pairing request (for testing)
  public simulatePairingRequest(request: PairingRequest): PairingResponse {
    const success = request.token === this.pairingToken;
    
    if (success) {
      const device: ConnectedDevice = {
        device_id: request.device_id,
        device_name: request.device_name,
        device_type: 'android', // Assume mobile device
        connected_at: Date.now(),
        last_seen: Date.now(),
      };
      
      this.handleDeviceConnected(device);
    }

    return {
      success,
      message: success ? 'Device paired successfully' : 'Invalid pairing token',
      server_device_id: this.deviceId,
    };
  }

  // Simulate receiving clipboard data (for testing)
  public simulateClipboardReceived(clipboardMsg: ClipboardMessage) {
    // Update device's current clipboard
    const device = this.connectedDevices.get(clipboardMsg.device_id);
    if (device) {
      device.current_clipboard = clipboardMsg.content;
      device.last_seen = Date.now();
      this.connectedDevices.set(clipboardMsg.device_id, device);
    }

    this.callbacks.onClipboardReceived?.(clipboardMsg);
  }
}

export default new MobileWebSocketServer();
