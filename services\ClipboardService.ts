import { Platform, AppState, AppStateStatus } from 'react-native';

// Simplified imports for development compatibility
let Clipboard: any = null;
let isClipboardAvailable = false;

try {
  Clipboard = require('@react-native-clipboard/clipboard').default;
  isClipboardAvailable = true;
  console.log('📋 Native clipboard module loaded successfully');
} catch (error) {
  console.log('📱 Development Mode: Using clipboard simulation (native module not available)');
}

// Simple types for development
interface ClipboardItem {
  id: string;
  content: string;
  timestamp: number;
  deviceId: string;
  deviceName: string;
  type: 'text' | 'rtf' | 'html';
  source: 'manual' | 'auto';
}

// Generate simple UUID
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

class ClipboardService {
  private monitoringInterval: ReturnType<typeof setInterval> | null = null;
  private lastClipboardContent: string = '';
  private isMonitoring: boolean = false;
  private monitoringIntervalMs: number = 1000; // Check every second by default
  private deviceId: string = '';
  private appState: AppStateStatus = 'active';
  private webClipboardHistory: ClipboardItem[] = []; // Fallback for web

  constructor() {
    // Simplified constructor for development
    this.initDeviceId();
    // Listen for app state changes to pause/resume monitoring
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  async getClipboardText(): Promise<string> {
    try {
      // Check if clipboard API is available (use global variable)

      if (Platform.OS === 'web') {
        // For web platform, use navigator.clipboard API
        if (navigator.clipboard && navigator.clipboard.readText) {
          return await navigator.clipboard.readText();
        } else {
          // Fallback for web - return cached content
          return this.lastClipboardContent || 'Demo clipboard content';
        }
      } else {
        // For mobile platforms - check if Clipboard is available
        if (isClipboardAvailable) {
          return await Clipboard.getString();
        } else {
          // Clipboard API not available (e.g., in Expo Go development)
          console.debug('📋 Development Mode: Using cached clipboard content (native API not available)');
          return this.lastClipboardContent || 'Demo clipboard content - Welcome to Clipsy!';
        }
      }
    } catch (error) {
      // Handle specific clipboard access errors gracefully
      if (error.name === 'NotAllowedError' ||
          error.message.includes('not focused') ||
          error.message.includes('Document is not focused') ||
          error.message.includes('readText') ||
          error.message.includes('not a function')) {
        // Document not focused, user interaction required, or API not available
        console.debug('📋 Clipboard access requires focus/interaction or API unavailable, using cached content');
        return this.lastClipboardContent || 'Demo clipboard content - Welcome to Clipsy!';
      }
      console.warn('⚠️ Failed to get clipboard text:', error);
      return this.lastClipboardContent || 'Demo clipboard content - Welcome to Clipsy!';
    }
  }

  async getCurrentClipboardText(): Promise<string> {
    return this.getClipboardText();
  }

  isClipboardAvailable(): boolean {
    if (Platform.OS === 'web') {
      return !!(navigator.clipboard && navigator.clipboard.readText && navigator.clipboard.writeText);
    } else {
      return !!(Clipboard && typeof Clipboard.getString === 'function' && typeof Clipboard.setString === 'function');
    }
  }

  getClipboardStatus(): { available: boolean; mode: string; message: string } {
    const available = this.isClipboardAvailable();

    if (available) {
      return {
        available: true,
        mode: 'native',
        message: 'Full clipboard functionality available'
      };
    } else {
      return {
        available: false,
        mode: 'development',
        message: 'Development mode - clipboard operations simulated (use production build for full functionality)'
      };
    }
  }

  async setClipboardText(text: string): Promise<void> {
    try {
      if (!text) {
        throw new Error('No text provided to copy');
      }

      // Check if we're in a development environment without full clipboard access (use global variable)

      if (!isClipboardAvailable) {
        // Development mode fallback - just store in memory and provide user feedback
        this.lastClipboardContent = text;
        console.log('📋 Development Mode: Text stored in memory cache (clipboard API not available)');

        // Add to web clipboard history for demo purposes
        const clipboardItem: ClipboardItem = {
          id: generateUUID(),
          content: text,
          timestamp: Date.now(),
          deviceId: this.deviceId || 'development-device',
          deviceName: 'Development Device',
          type: 'text',
          source: 'manual'
        };

        this.webClipboardHistory.unshift(clipboardItem);
        if (this.webClipboardHistory.length > 50) {
          this.webClipboardHistory = this.webClipboardHistory.slice(0, 50);
        }

        // Don't throw error in development mode - just simulate success
        return;
      }

      if (Platform.OS === 'web') {
        // For web platform, use navigator.clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(text);
        } else {
          // Fallback for web - create a temporary textarea
          const textArea = document.createElement('textarea');
          textArea.value = text;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);

          if (!successful) {
            throw new Error('Failed to copy using fallback method');
          }
        }
      } else {
        // For mobile platforms - use native clipboard
        await Clipboard.setString(text);
      }

      this.lastClipboardContent = text;
      console.log('✅ Successfully set clipboard text:', text.substring(0, 50) + '...');
    } catch (error) {
      console.error('❌ Failed to set clipboard text:', error);

      // In development mode, don't throw errors for clipboard unavailability
      if (!Clipboard || typeof Clipboard.setString !== 'function') {
        console.log('📋 Development Mode: Clipboard operation simulated (native API not available)');
        this.lastClipboardContent = text;
        return;
      }

      // Provide more specific error information for production
      if (error.name === 'NotAllowedError') {
        throw new Error('Clipboard access denied - please grant permission');
      } else if (error.message && error.message.includes('not focused')) {
        throw new Error('App needs focus to access clipboard');
      } else {
        throw new Error(`Clipboard operation failed: ${error.message || 'Unknown error'}`);
      }
    }
  }

  async saveToHistory(content: string, deviceId: string, contentType: 'text' | 'rtf' | 'html' = 'text'): Promise<void> {
    const item: ClipboardItem = {
      id: generateUUID(),
      content,
      timestamp: Date.now(),
      deviceId,
      deviceName: 'Development Device',
      type: contentType,
      source: 'auto'
    };

    // Simplified in-memory storage for development
    this.webClipboardHistory.unshift(item);
    // Keep only last 50 items in memory
    if (this.webClipboardHistory.length > 50) {
      this.webClipboardHistory = this.webClipboardHistory.slice(0, 50);
    }
    console.log('📋 Clipboard item saved to memory cache');
  }

  async getHistory(limit: number = 20): Promise<ClipboardItem[]> {
    // Handle web platform with in-memory storage
    if (Platform.OS === 'web' || !this.db) {
      return Promise.resolve(this.webClipboardHistory.slice(0, limit));
    }

    // Handle native platforms with SQLite
    return new Promise((resolve, reject) => {
      this.db!.transaction(tx => {
        tx.executeSql(
          `SELECT * FROM clipboard_items
           ORDER BY timestamp DESC
           LIMIT ?`,
          [limit],
          (_, result) => {
            const items: ClipboardItem[] = [];
            for (let i = 0; i < result.rows.length; i++) {
              const row = result.rows.item(i);
              items.push({
                id: row.id,
                content: row.content,
                timestamp: new Date(row.timestamp),
                deviceId: row.device_id,
                contentType: row.content_type as 'text' | 'rtf' | 'html',
              });
            }
            resolve(items);
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  async deleteItem(id: string): Promise<void> {
    // Handle web platform with in-memory storage
    if (Platform.OS === 'web' || !this.db) {
      this.webClipboardHistory = this.webClipboardHistory.filter(item => item.id !== id);
      return Promise.resolve();
    }

    // Handle native platforms with SQLite
    return new Promise((resolve, reject) => {
      this.db!.transaction(tx => {
        tx.executeSql(
          'DELETE FROM clipboard_items WHERE id = ?',
          [id],
          (_, result) => {
            resolve();
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  private initDeviceId(): void {
    try {
      // Simplified device ID generation for development
      if (Platform.OS === 'web') {
        // For web, generate a persistent ID
        const storedId = localStorage.getItem('clipbee_device_id');
        if (storedId) {
          this.deviceId = storedId;
        } else {
          this.deviceId = generateUUID();
          localStorage.setItem('clipbee_device_id', this.deviceId);
        }
      } else {
        // For mobile platforms, generate a simple UUID
        this.deviceId = generateUUID();
      }
    } catch (error) {
      console.error('Failed to get device ID:', error);
      // Fallback to a random ID
      this.deviceId = generateUUID();
    }
  }

  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    if (this.appState !== nextAppState) {
      this.appState = nextAppState;
      
      if (nextAppState === 'active') {
        // App came to foreground, resume monitoring if it was active before
        if (this.isMonitoring && !this.monitoringInterval) {
          this.startMonitoring(this.monitoringIntervalMs);
        }
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App went to background, pause monitoring to save resources
        this.pauseMonitoring();
      }
    }
  };

  startMonitoring(intervalMs: number = 1000): void {
    // Don't start if already monitoring
    if (this.monitoringInterval) {
      return;
    }

    this.monitoringIntervalMs = intervalMs;
    this.isMonitoring = true;

    // Initialize with current clipboard content
    this.getClipboardText().then(content => {
      this.lastClipboardContent = content;
    }).catch(error => {
      // Handle clipboard initialization errors gracefully
      if (error.message && error.message.includes('not a function')) {
        console.debug('Clipboard API not available, using demo content');
        this.lastClipboardContent = 'Demo clipboard content';
      } else {
        console.error('Error getting initial clipboard content:', error);
        this.lastClipboardContent = 'Demo clipboard content';
      }
    });

    // Start monitoring interval
    this.monitoringInterval = setInterval(async () => {
      try {
        // Only check when app is active
        if (this.appState !== 'active') return;

        const currentContent = await this.getClipboardText();

        // Check if clipboard content has changed
        if (currentContent !== this.lastClipboardContent && currentContent.trim() !== '') {
          console.log('Clipboard change detected:', currentContent);
          this.lastClipboardContent = currentContent;

          // Save to history if we have a device ID
          if (this.deviceId) {
            await this.saveToHistory(currentContent, this.deviceId);
          }
        }
      } catch (error) {
        // Handle clipboard monitoring errors gracefully
        if (error.name === 'NotAllowedError' ||
            error.message.includes('not focused') ||
            error.message.includes('Document is not focused') ||
            error.message.includes('readText') ||
            error.message.includes('not a function')) {
          // Silently skip this monitoring cycle - document not focused or API not available
          console.debug('Clipboard monitoring skipped - document not focused or API unavailable');
        } else {
          console.error('Error in clipboard monitoring:', error);
        }
      }
    }, intervalMs);

    console.log('Clipboard monitoring started');
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Clipboard monitoring stopped');
  }

  pauseMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    // Keep isMonitoring true so we know to resume when app becomes active again
  }

  isClipboardMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  getDeviceId(): string {
    return this.deviceId;
  }
}



export default new ClipboardService(); 