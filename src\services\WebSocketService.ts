import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ConnectionInfo {
  ip: string;
  port: number;
  token: string;
  device_id: string;
}

export interface PairingRequest {
  device_id: string;
  device_name: string;
  token: string;
}

export interface PairingResponse {
  success: boolean;
  message: string;
  server_device_id: string;
}

export interface ClipboardMessage {
  content: string;
  timestamp: number;
  device_id: string;
  content_type: string;
}

export interface WebSocketMessage {
  type: 'Ping' | 'Pong' | 'PairingRequest' | 'PairingResponse' | 'ClipboardSync' | 'DeviceStatus';
  [key: string]: any;
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'pairing' | 'error';

export interface WebSocketServiceCallbacks {
  onConnectionStatusChange?: (status: ConnectionStatus) => void;
  onPairingResponse?: (response: PairingResponse) => void;
  onClipboardReceived?: (clipboard: ClipboardMessage) => void;
  onError?: (error: string) => void;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private connectionInfo: ConnectionInfo | null = null;
  private callbacks: WebSocketServiceCallbacks = {};
  private status: ConnectionStatus = 'disconnected';
  private deviceId: string = '';
  private deviceName: string = 'ClipSync Mobile';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.generateDeviceId();
  }

  private async generateDeviceId() {
    try {
      let deviceId = await AsyncStorage.getItem('device_id');
      if (!deviceId) {
        deviceId = `mobile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('device_id', deviceId);
      }
      this.deviceId = deviceId;
    } catch (error) {
      console.error('Failed to generate device ID:', error);
      this.deviceId = `mobile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }

  public setCallbacks(callbacks: WebSocketServiceCallbacks) {
    this.callbacks = callbacks;
  }

  public getConnectionStatus(): ConnectionStatus {
    return this.status;
  }

  public async connect(connectionInfo: ConnectionInfo): Promise<boolean> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
    }

    this.connectionInfo = connectionInfo;
    this.setStatus('connecting');

    try {
      const wsUrl = `ws://${connectionInfo.ip}:${connectionInfo.port}`;
      console.log('Connecting to:', wsUrl);

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.setStatus('pairing');
        this.reconnectAttempts = 0;
        this.startPairing();
        this.startPingInterval();
      };

      this.ws.onmessage = (event) => {
        this.handleMessage(event.data);
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        this.setStatus('disconnected');
        this.stopPingInterval();
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.setStatus('error');
        this.callbacks.onError?.('Connection failed');
      };

      return true;
    } catch (error) {
      console.error('Failed to connect:', error);
      this.setStatus('error');
      this.callbacks.onError?.('Failed to establish connection');
      return false;
    }
  }

  public disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    this.stopPingInterval();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.setStatus('disconnected');
  }

  private setStatus(status: ConnectionStatus) {
    if (this.status !== status) {
      this.status = status;
      this.callbacks.onConnectionStatusChange?.(status);
    }
  }

  private startPairing() {
    if (!this.connectionInfo) return;

    const pairingRequest: PairingRequest = {
      device_id: this.deviceId,
      device_name: this.deviceName,
      token: this.connectionInfo.token,
    };

    const message: WebSocketMessage = {
      type: 'PairingRequest',
      ...pairingRequest,
    };

    this.sendMessage(message);
  }

  private handleMessage(data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data);

      switch (message.type) {
        case 'PairingResponse':
          this.handlePairingResponse(message as any);
          break;
        case 'ClipboardSync':
          this.handleClipboardSync(message as any);
          break;
        case 'Ping':
          this.sendPong();
          break;
        case 'DeviceStatus':
          console.log('Device status:', message);
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  }

  private handlePairingResponse(response: PairingResponse) {
    console.log('Pairing response:', response);
    
    if (response.success) {
      this.setStatus('connected');
      console.log('Successfully paired with desktop');
    } else {
      this.setStatus('error');
      this.callbacks.onError?.(response.message);
    }
    
    this.callbacks.onPairingResponse?.(response);
  }

  private handleClipboardSync(clipboardMsg: ClipboardMessage) {
    console.log('Received clipboard:', clipboardMsg.content);
    this.callbacks.onClipboardReceived?.(clipboardMsg);
  }

  private sendMessage(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private sendPong() {
    const message: WebSocketMessage = { type: 'Pong' };
    this.sendMessage(message);
  }

  private startPingInterval() {
    this.pingInterval = setInterval(() => {
      const message: WebSocketMessage = { type: 'Ping' };
      this.sendMessage(message);
    }, 30000); // Ping every 30 seconds
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      if (this.connectionInfo) {
        console.log('Attempting to reconnect...');
        this.connect(this.connectionInfo);
      }
    }, delay);
  }

  public sendClipboard(content: string, contentType: string = 'text') {
    if (this.status !== 'connected') {
      console.warn('Cannot send clipboard - not connected');
      return;
    }

    const clipboardMsg: ClipboardMessage = {
      content,
      timestamp: Date.now(),
      device_id: this.deviceId,
      content_type: contentType,
    };

    const message: WebSocketMessage = {
      type: 'ClipboardSync',
      ...clipboardMsg,
    };

    this.sendMessage(message);
  }

  public async saveConnectionInfo(info: ConnectionInfo) {
    try {
      await AsyncStorage.setItem('last_connection_info', JSON.stringify(info));
    } catch (error) {
      console.error('Failed to save connection info:', error);
    }
  }

  public async getLastConnectionInfo(): Promise<ConnectionInfo | null> {
    try {
      const data = await AsyncStorage.getItem('last_connection_info');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get last connection info:', error);
      return null;
    }
  }
}

export default new WebSocketService();
