import AsyncStorage from '@react-native-async-storage/async-storage';
import { crossPlatformSyncProtocol } from './CrossPlatformSyncProtocol';

export interface ConnectionInfo {
  ip: string;
  port: number;
  token: string;
  device_id: string;
  encryptionKey?: string;
  sessionId?: string;
}

export interface SecureConnectionInfo extends ConnectionInfo {
  publicKey: string;
  certificateChain?: string[];
  securityLevel: 'basic' | 'standard' | 'high';
}

export interface PairingRequest {
  device_id: string;
  device_name: string;
  token: string;
}

export interface PairingResponse {
  success: boolean;
  message: string;
  server_device_id: string;
}

export interface ClipboardMessage {
  content: string;
  timestamp: number;
  device_id: string;
  content_type: string;
}

export interface WebSocketMessage {
  type: 'Ping' | 'Pong' | 'PairingRequest' | 'PairingResponse' | 'ClipboardSync' | 'DeviceStatus' |
        'SecureHandshake' | 'EncryptedMessage' | 'KeyExchange' | 'AuthChallenge' | 'AuthResponse';
  [key: string]: any;
  encrypted?: boolean;
  signature?: string;
  nonce?: string;
  sessionId?: string;
}

export interface EncryptedMessage {
  encryptedData: string;
  iv: string;
  authTag: string;
  timestamp: number;
}

export interface SecurityContext {
  sessionId: string;
  encryptionKey: string;
  signingKey: string;
  devicePublicKey: string;
  isAuthenticated: boolean;
  securityLevel: 'basic' | 'standard' | 'high';
  expiresAt: number;
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'pairing' | 'error';

export interface WebSocketServiceCallbacks {
  onConnectionStatusChange?: (status: ConnectionStatus) => void;
  onPairingResponse?: (response: PairingResponse) => void;
  onClipboardReceived?: (clipboard: ClipboardMessage) => void;
  onError?: (error: string) => void;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private connectionInfo: ConnectionInfo | null = null;
  private callbacks: WebSocketServiceCallbacks = {};
  private status: ConnectionStatus = 'disconnected';
  private deviceId: string = '';
  private deviceName: string = 'ClipSync Mobile';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private pingInterval: NodeJS.Timeout | null = null;

  // Security-related properties
  private securityContext: SecurityContext | null = null;
  private encryptionEnabled: boolean = true;
  private deviceKeys: { publicKey: string; privateKey: string } | null = null;
  private trustedDevices: Set<string> = new Set();
  private securityLevel: 'basic' | 'standard' | 'high' = 'standard';

  constructor() {
    this.generateDeviceId();
    this.initializeSecurity();
  }

  private async generateDeviceId() {
    try {
      let deviceId = await AsyncStorage.getItem('device_id');
      if (!deviceId) {
        deviceId = `mobile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('device_id', deviceId);
      }
      this.deviceId = deviceId;
    } catch (error) {
      console.error('Failed to generate device ID:', error);
      this.deviceId = `mobile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }

  /**
   * Initialize security components
   */
  private async initializeSecurity() {
    try {
      await this.generateDeviceKeys();
      await this.loadTrustedDevices();
      console.log('Security initialized successfully');
    } catch (error) {
      console.error('Failed to initialize security:', error);
    }
  }

  /**
   * Generate device encryption keys
   */
  private async generateDeviceKeys() {
    try {
      let keys = await AsyncStorage.getItem('device_keys');

      if (!keys) {
        // Generate new key pair (simplified for demo)
        const keyPair = {
          publicKey: `pub_${this.deviceId}_${Date.now()}`,
          privateKey: `priv_${this.deviceId}_${Date.now()}`
        };

        await AsyncStorage.setItem('device_keys', JSON.stringify(keyPair));
        this.deviceKeys = keyPair;
        console.log('Generated new device keys');
      } else {
        this.deviceKeys = JSON.parse(keys);
        console.log('Loaded existing device keys');
      }
    } catch (error) {
      console.error('Failed to generate device keys:', error);
      // Fallback to temporary keys
      this.deviceKeys = {
        publicKey: `temp_pub_${Date.now()}`,
        privateKey: `temp_priv_${Date.now()}`
      };
    }
  }

  /**
   * Load trusted devices from storage
   */
  private async loadTrustedDevices() {
    try {
      const trustedDevicesData = await AsyncStorage.getItem('trusted_devices');
      if (trustedDevicesData) {
        const devices = JSON.parse(trustedDevicesData);
        this.trustedDevices = new Set(devices);
        console.log(`Loaded ${this.trustedDevices.size} trusted devices`);
      }
    } catch (error) {
      console.error('Failed to load trusted devices:', error);
    }
  }

  /**
   * Save trusted devices to storage
   */
  private async saveTrustedDevices() {
    try {
      const devices = Array.from(this.trustedDevices);
      await AsyncStorage.setItem('trusted_devices', JSON.stringify(devices));
    } catch (error) {
      console.error('Failed to save trusted devices:', error);
    }
  }

  /**
   * Encrypt message content
   */
  private encryptMessage(message: any): EncryptedMessage {
    // Simplified encryption for demo - in production use proper crypto libraries
    const data = JSON.stringify(message);
    const timestamp = Date.now();
    const iv = Math.random().toString(36).substr(2, 16);
    const authTag = this.generateAuthTag(data, iv);

    // Simple XOR encryption (replace with AES in production)
    const encryptedData = this.simpleEncrypt(data, this.securityContext?.encryptionKey || 'default');

    return {
      encryptedData,
      iv,
      authTag,
      timestamp
    };
  }

  /**
   * Decrypt message content
   */
  private decryptMessage(encrypted: EncryptedMessage): any {
    try {
      // Verify auth tag
      const expectedTag = this.generateAuthTag(encrypted.encryptedData, encrypted.iv);
      if (expectedTag !== encrypted.authTag) {
        throw new Error('Message authentication failed');
      }

      // Decrypt data
      const decryptedData = this.simpleDecrypt(encrypted.encryptedData, this.securityContext?.encryptionKey || 'default');
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      throw error;
    }
  }

  /**
   * Simple encryption (replace with proper AES in production)
   */
  private simpleEncrypt(data: string, key: string): string {
    let result = '';
    for (let i = 0; i < data.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      const dataChar = data.charCodeAt(i);
      result += String.fromCharCode(dataChar ^ keyChar);
    }
    return btoa(result); // Base64 encode
  }

  /**
   * Simple decryption (replace with proper AES in production)
   */
  private simpleDecrypt(encryptedData: string, key: string): string {
    const data = atob(encryptedData); // Base64 decode
    let result = '';
    for (let i = 0; i < data.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      const dataChar = data.charCodeAt(i);
      result += String.fromCharCode(dataChar ^ keyChar);
    }
    return result;
  }

  /**
   * Generate authentication tag
   */
  private generateAuthTag(data: string, iv: string): string {
    // Simplified HMAC (replace with proper HMAC-SHA256 in production)
    const combined = data + iv + (this.securityContext?.signingKey || 'default');
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Establish secure session
   */
  private async establishSecureSession(deviceId: string): Promise<boolean> {
    try {
      // Check if device is trusted
      const isTrusted = this.trustedDevices.has(deviceId);

      // Generate session keys
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const encryptionKey = `enc_${sessionId}_${Math.random().toString(36).substr(2, 16)}`;
      const signingKey = `sign_${sessionId}_${Math.random().toString(36).substr(2, 16)}`;

      this.securityContext = {
        sessionId,
        encryptionKey,
        signingKey,
        devicePublicKey: this.deviceKeys?.publicKey || '',
        isAuthenticated: isTrusted,
        securityLevel: this.securityLevel,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };

      console.log('Secure session established:', sessionId);
      return true;
    } catch (error) {
      console.error('Failed to establish secure session:', error);
      return false;
    }
  }

  public setCallbacks(callbacks: WebSocketServiceCallbacks) {
    this.callbacks = callbacks;
  }

  public getConnectionStatus(): ConnectionStatus {
    return this.status;
  }

  public async connect(connectionInfo: ConnectionInfo): Promise<boolean> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
    }

    this.connectionInfo = connectionInfo;
    this.setStatus('connecting');

    try {
      const wsUrl = `ws://${connectionInfo.ip}:${connectionInfo.port}`;
      console.log('Connecting to:', wsUrl);

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.setStatus('pairing');
        this.reconnectAttempts = 0;
        this.startPairing();
        this.startPingInterval();
      };

      this.ws.onmessage = (event) => {
        this.handleMessage(event.data);
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        this.setStatus('disconnected');
        this.stopPingInterval();
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.setStatus('error');
        this.callbacks.onError?.('Connection failed');
      };

      return true;
    } catch (error) {
      console.error('Failed to connect:', error);
      this.setStatus('error');
      this.callbacks.onError?.('Failed to establish connection');
      return false;
    }
  }

  public disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    this.stopPingInterval();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.setStatus('disconnected');
  }

  private setStatus(status: ConnectionStatus) {
    if (this.status !== status) {
      this.status = status;
      this.callbacks.onConnectionStatusChange?.(status);
    }
  }

  private startPairing() {
    if (!this.connectionInfo) return;

    const pairingRequest: PairingRequest = {
      device_id: this.deviceId,
      device_name: this.deviceName,
      token: this.connectionInfo.token,
    };

    const message: WebSocketMessage = {
      type: 'PairingRequest',
      ...pairingRequest,
    };

    this.sendMessage(message);
  }

  private handleMessage(data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data);

      // Handle encrypted messages
      if (message.encrypted && message.type === 'EncryptedMessage') {
        const decryptedMessage = this.decryptMessage(message.data as EncryptedMessage);
        this.handleMessage(JSON.stringify(decryptedMessage));
        return;
      }

      // Verify message signature if present
      if (message.signature && !this.verifyMessageSignature(message)) {
        console.error('Message signature verification failed');
        return;
      }

      switch (message.type) {
        case 'PairingResponse':
          this.handlePairingResponse(message as any);
          break;
        case 'ClipboardSync':
          this.handleClipboardSync(message as any);
          break;
        case 'Ping':
          this.sendPong();
          break;
        case 'DeviceStatus':
          console.log('Device status:', message);
          break;
        case 'SecureHandshake':
          this.handleSecureHandshake(message);
          break;
        case 'KeyExchange':
          this.handleKeyExchange(message);
          break;
        case 'AuthChallenge':
          this.handleAuthChallenge(message);
          break;
        case 'AuthResponse':
          this.handleAuthResponse(message);
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  }

  /**
   * Verify message signature
   */
  private verifyMessageSignature(message: WebSocketMessage): boolean {
    try {
      // Simplified signature verification (replace with proper crypto in production)
      const messageData = { ...message };
      delete messageData.signature;
      const expectedSignature = this.generateMessageSignature(messageData);
      return expectedSignature === message.signature;
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  /**
   * Generate message signature
   */
  private generateMessageSignature(message: any): string {
    // Simplified signature generation (replace with proper HMAC in production)
    const data = JSON.stringify(message);
    const key = this.securityContext?.signingKey || 'default';
    return this.generateAuthTag(data, key);
  }

  /**
   * Handle secure handshake
   */
  private async handleSecureHandshake(message: WebSocketMessage) {
    try {
      console.log('Handling secure handshake');
      const deviceId = message.deviceId;

      if (deviceId) {
        await this.establishSecureSession(deviceId);

        // Send handshake response
        const response = {
          type: 'SecureHandshake',
          deviceId: this.deviceId,
          sessionId: this.securityContext?.sessionId,
          publicKey: this.deviceKeys?.publicKey,
          securityLevel: this.securityLevel,
          timestamp: Date.now()
        };

        this.sendSecureMessage(response);
      }
    } catch (error) {
      console.error('Secure handshake failed:', error);
    }
  }

  /**
   * Handle key exchange
   */
  private handleKeyExchange(message: WebSocketMessage) {
    try {
      console.log('Handling key exchange');
      // In a real implementation, this would handle Diffie-Hellman key exchange
      // For now, just acknowledge the key exchange
      const response = {
        type: 'KeyExchange',
        deviceId: this.deviceId,
        publicKey: this.deviceKeys?.publicKey,
        timestamp: Date.now()
      };

      this.sendSecureMessage(response);
    } catch (error) {
      console.error('Key exchange failed:', error);
    }
  }

  /**
   * Handle authentication challenge
   */
  private async handleAuthChallenge(message: WebSocketMessage) {
    try {
      console.log('Handling authentication challenge');
      const challenge = message.data;

      // Generate response based on challenge type
      const response = await this.generateAuthResponse(challenge);

      const authResponse = {
        type: 'AuthResponse',
        deviceId: this.deviceId,
        challengeId: challenge.challengeId,
        response,
        timestamp: Date.now()
      };

      this.sendSecureMessage(authResponse);
    } catch (error) {
      console.error('Authentication challenge handling failed:', error);
    }
  }

  /**
   * Handle authentication response
   */
  private handleAuthResponse(message: WebSocketMessage) {
    try {
      console.log('Handling authentication response');
      // Verify the authentication response
      const isValid = this.verifyAuthResponse(message.data);

      if (isValid && this.securityContext) {
        this.securityContext.isAuthenticated = true;
        console.log('Device authenticated successfully');
      } else {
        console.error('Authentication failed');
        this.disconnect();
      }
    } catch (error) {
      console.error('Authentication response handling failed:', error);
    }
  }

  private handlePairingResponse(response: PairingResponse) {
    console.log('Pairing response:', response);
    
    if (response.success) {
      this.setStatus('connected');
      console.log('Successfully paired with desktop');
    } else {
      this.setStatus('error');
      this.callbacks.onError?.(response.message);
    }
    
    this.callbacks.onPairingResponse?.(response);
  }

  private handleClipboardSync(clipboardMsg: ClipboardMessage) {
    console.log('Received clipboard:', clipboardMsg.content);
    this.callbacks.onClipboardReceived?.(clipboardMsg);
  }

  private sendMessage(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      // Add signature to message if security context exists
      if (this.securityContext && this.securityContext.isAuthenticated) {
        message.signature = this.generateMessageSignature(message);
        message.sessionId = this.securityContext.sessionId;
      }

      this.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Send encrypted message
   */
  private sendSecureMessage(message: any) {
    if (!this.encryptionEnabled || !this.securityContext) {
      // Fall back to regular message if encryption not available
      this.sendMessage(message);
      return;
    }

    try {
      const encryptedData = this.encryptMessage(message);
      const secureMessage: WebSocketMessage = {
        type: 'EncryptedMessage',
        encrypted: true,
        data: encryptedData,
        sessionId: this.securityContext.sessionId,
        timestamp: Date.now()
      };

      this.sendMessage(secureMessage);
    } catch (error) {
      console.error('Failed to send secure message:', error);
      // Fall back to regular message
      this.sendMessage(message);
    }
  }

  private sendPong() {
    const message: WebSocketMessage = { type: 'Pong' };
    this.sendMessage(message);
  }

  private startPingInterval() {
    this.pingInterval = setInterval(() => {
      const message: WebSocketMessage = { type: 'Ping' };
      this.sendMessage(message);
    }, 30000); // Ping every 30 seconds
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      if (this.connectionInfo) {
        console.log('Attempting to reconnect...');
        this.connect(this.connectionInfo);
      }
    }, delay);
  }

  public sendClipboard(content: string, contentType: string = 'text') {
    if (this.status !== 'connected') {
      console.warn('Cannot send clipboard - not connected');
      return;
    }

    const clipboardMsg: ClipboardMessage = {
      content,
      timestamp: Date.now(),
      device_id: this.deviceId,
      content_type: contentType,
    };

    const message: WebSocketMessage = {
      type: 'ClipboardSync',
      ...clipboardMsg,
    };

    this.sendMessage(message);
  }

  public async saveConnectionInfo(info: ConnectionInfo) {
    try {
      await AsyncStorage.setItem('last_connection_info', JSON.stringify(info));
    } catch (error) {
      console.error('Failed to save connection info:', error);
    }
  }

  public async getLastConnectionInfo(): Promise<ConnectionInfo | null> {
    try {
      const data = await AsyncStorage.getItem('last_connection_info');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get last connection info:', error);
      return null;
    }
  }

  /**
   * Generate authentication response
   */
  private async generateAuthResponse(challenge: any): Promise<string> {
    try {
      switch (challenge.challengeType) {
        case 'token':
          return challenge.challengeData;
        case 'pin':
          // In a real app, this would prompt user for PIN
          return challenge.challengeData;
        case 'certificate':
          return `cert_response_${this.deviceKeys?.privateKey}`;
        case 'biometric':
          return `bio_response_${this.deviceId}`;
        default:
          return 'default_response';
      }
    } catch (error) {
      console.error('Failed to generate auth response:', error);
      throw error;
    }
  }

  /**
   * Verify authentication response
   */
  private verifyAuthResponse(authData: any): boolean {
    try {
      // Simplified verification (replace with proper verification in production)
      return authData && authData.response && authData.challengeId;
    } catch (error) {
      console.error('Auth response verification failed:', error);
      return false;
    }
  }

  /**
   * Add device to trusted list
   */
  public async addTrustedDevice(deviceId: string): Promise<void> {
    try {
      this.trustedDevices.add(deviceId);
      await this.saveTrustedDevices();
      console.log(`Device added to trusted list: ${deviceId}`);
    } catch (error) {
      console.error('Failed to add trusted device:', error);
    }
  }

  /**
   * Remove device from trusted list
   */
  public async removeTrustedDevice(deviceId: string): Promise<void> {
    try {
      this.trustedDevices.delete(deviceId);
      await this.saveTrustedDevices();
      console.log(`Device removed from trusted list: ${deviceId}`);
    } catch (error) {
      console.error('Failed to remove trusted device:', error);
    }
  }

  /**
   * Check if device is trusted
   */
  public isDeviceTrusted(deviceId: string): boolean {
    return this.trustedDevices.has(deviceId);
  }

  /**
   * Get security context
   */
  public getSecurityContext(): SecurityContext | null {
    return this.securityContext;
  }

  /**
   * Set security level
   */
  public setSecurityLevel(level: 'basic' | 'standard' | 'high'): void {
    this.securityLevel = level;
    console.log(`Security level set to: ${level}`);
  }

  /**
   * Enable/disable encryption
   */
  public setEncryptionEnabled(enabled: boolean): void {
    this.encryptionEnabled = enabled;
    console.log(`Encryption ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get device public key
   */
  public getDevicePublicKey(): string | null {
    return this.deviceKeys?.publicKey || null;
  }

  /**
   * Clean up expired security contexts
   */
  public cleanupSecurity(): void {
    if (this.securityContext && Date.now() > this.securityContext.expiresAt) {
      console.log('Security context expired, cleaning up');
      this.securityContext = null;
    }
  }
}

export default new WebSocketService();
