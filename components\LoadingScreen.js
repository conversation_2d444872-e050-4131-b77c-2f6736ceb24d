import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

/**
 * A simple loading screen component to display during dynamic imports
 */
const LoadingScreen = () => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#0066CC" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
});

export default LoadingScreen;
