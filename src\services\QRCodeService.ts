/**
 * QR Code Service for Clipsy Android App
 * Handles QR code scanning for device pairing and connection
 */

import { Platform } from 'react-native';

// Platform-specific imports
let Camera: any = null;
if (Platform.OS !== 'web') {
  try {
    Camera = require('expo-camera').Camera;
  } catch (error) {
    console.warn('expo-camera not available on this platform:', error);
  }
}
import { Alert } from 'react-native';
import WebSocketService, { ConnectionInfo } from './WebSocketService';

export interface QRCodeData {
  ip: string;
  port: number;
  token: string;
  device_id: string;
  device_name: string;
  version?: string;
}

export interface QRScanResult {
  success: boolean;
  data?: QRCodeData;
  error?: string;
}

export interface QRCodeServiceCallbacks {
  onScanSuccess: (data: QRCodeData) => void;
  onScanError: (error: string) => void;
  onPermissionDenied: () => void;
  onConnectionAttempt: (data: QRCodeData) => void;
  onConnectionSuccess: () => void;
  onConnectionError: (error: string) => void;
}

class QRCodeService {
  private callbacks: QRCodeServiceCallbacks | null = null;
  private isScanning = false;

  /**
   * Initialize QR code service with callbacks
   */
  initialize(callbacks: QRCodeServiceCallbacks) {
    this.callbacks = callbacks;
  }

  /**
   * Request camera permissions for QR code scanning
   */
  async requestCameraPermissions(): Promise<boolean> {
    if (Platform.OS === 'web' || !Camera) {
      console.warn('Camera permissions not available on this platform');
      this.callbacks?.onPermissionDenied();
      return false;
    }

    try {
      const { status } = await Camera.requestCameraPermissionsAsync();

      if (status === 'granted') {
        return true;
      } else {
        this.callbacks?.onPermissionDenied();
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera access in settings to scan QR codes for device pairing.',
          [{ text: 'OK' }]
        );
        return false;
      }
    } catch (error) {
      console.error('Failed to request camera permissions:', error);
      this.callbacks?.onScanError('Failed to request camera permissions');
      return false;
    }
  }

  /**
   * Check if camera permissions are granted
   */
  async hasCameraPermissions(): Promise<boolean> {
    if (Platform.OS === 'web' || !BarCodeScanner) {
      console.warn('Camera permissions not available on this platform');
      return false;
    }

    try {
      const { status } = await BarCodeScanner.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Failed to check camera permissions:', error);
      return false;
    }
  }

  /**
   * Parse QR code data from scanned string
   */
  parseQRCodeData(data: string): QRScanResult {
    try {
      // Expected format: clipsy://connect?ip=*************&port=8080&token=abc123&device_id=desktop_123&device_name=Desktop%20PC
      const url = new URL(data);
      
      if (url.protocol !== 'clipsy:' || url.hostname !== 'connect') {
        return {
          success: false,
          error: 'Invalid QR code format. Please scan a valid Clipsy pairing QR code.'
        };
      }

      const params = url.searchParams;
      const ip = params.get('ip');
      const port = params.get('port');
      const token = params.get('token');
      const device_id = params.get('device_id');
      const device_name = params.get('device_name');

      if (!ip || !port || !token || !device_id || !device_name) {
        return {
          success: false,
          error: 'QR code is missing required connection information.'
        };
      }

      const qrData: QRCodeData = {
        ip,
        port: parseInt(port, 10),
        token,
        device_id,
        device_name: decodeURIComponent(device_name),
        version: params.get('version') || '1.0'
      };

      // Validate IP address format
      if (!this.isValidIP(ip)) {
        return {
          success: false,
          error: 'Invalid IP address in QR code.'
        };
      }

      // Validate port number
      if (isNaN(qrData.port) || qrData.port < 1 || qrData.port > 65535) {
        return {
          success: false,
          error: 'Invalid port number in QR code.'
        };
      }

      return {
        success: true,
        data: qrData
      };
    } catch (error) {
      console.error('Failed to parse QR code data:', error);
      return {
        success: false,
        error: 'Failed to parse QR code. Please try scanning again.'
      };
    }
  }

  /**
   * Handle QR code scan result
   */
  handleQRCodeScan(data: string) {
    if (this.isScanning) {
      return; // Prevent multiple scans
    }

    this.isScanning = true;

    const result = this.parseQRCodeData(data);
    
    if (result.success && result.data) {
      console.log('QR code scanned successfully:', result.data);
      this.callbacks?.onScanSuccess(result.data);
      
      // Automatically attempt connection
      this.attemptConnection(result.data);
    } else {
      console.error('QR code scan failed:', result.error);
      this.callbacks?.onScanError(result.error || 'Unknown error');
      
      Alert.alert(
        'QR Code Error',
        result.error || 'Failed to scan QR code',
        [{ text: 'OK', onPress: () => this.resetScanning() }]
      );
    }
  }

  /**
   * Attempt to connect to device using QR code data
   */
  private async attemptConnection(qrData: QRCodeData) {
    try {
      this.callbacks?.onConnectionAttempt(qrData);

      const connectionInfo: ConnectionInfo = {
        ip: qrData.ip,
        port: qrData.port,
        token: qrData.token,
        device_id: qrData.device_id
      };

      console.log('Attempting to connect to:', connectionInfo);

      // Save connection info for future use
      await WebSocketService.saveConnectionInfo(connectionInfo);

      // Attempt WebSocket connection
      const connected = await WebSocketService.connect(connectionInfo);

      if (connected) {
        console.log('Connection successful');
        this.callbacks?.onConnectionSuccess();
        
        Alert.alert(
          'Connection Successful',
          `Successfully connected to ${qrData.device_name}`,
          [{ text: 'OK', onPress: () => this.resetScanning() }]
        );
      } else {
        throw new Error('Failed to establish WebSocket connection');
      }
    } catch (error) {
      console.error('Connection failed:', error);
      const errorMessage = `Failed to connect to ${qrData.device_name}. Please try again.`;
      this.callbacks?.onConnectionError(errorMessage);
      
      Alert.alert(
        'Connection Failed',
        errorMessage,
        [{ text: 'OK', onPress: () => this.resetScanning() }]
      );
    }
  }

  /**
   * Reset scanning state to allow new scans
   */
  resetScanning() {
    this.isScanning = false;
  }

  /**
   * Validate IP address format
   */
  private isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  /**
   * Get scanning status
   */
  getIsScanning(): boolean {
    return this.isScanning;
  }

  /**
   * Generate QR code data for Android-to-Android connections
   * This allows Android devices to act as hosts for other devices
   */
  generateQRCodeData(serverPort: number, deviceName: string): string {
    const token = this.generateSecureToken();
    const deviceId = `android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Get local IP address (this would need to be implemented)
    const localIP = '*************'; // Placeholder - would need actual IP detection
    
    const qrData = {
      ip: localIP,
      port: serverPort,
      token,
      device_id: deviceId,
      device_name: encodeURIComponent(deviceName),
      version: '1.0'
    };

    const qrUrl = `clipsy://connect?ip=${qrData.ip}&port=${qrData.port}&token=${qrData.token}&device_id=${qrData.device_id}&device_name=${qrData.device_name}&version=${qrData.version}`;
    
    return qrUrl;
  }

  /**
   * Generate secure token for device pairing
   */
  private generateSecureToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return token;
  }

  /**
   * Create connection info from QR data
   */
  createConnectionInfo(qrData: QRCodeData): ConnectionInfo {
    return {
      ip: qrData.ip,
      port: qrData.port,
      token: qrData.token,
      device_id: qrData.device_id
    };
  }
}

// Export singleton instance
export const qrCodeService = new QRCodeService();
export default QRCodeService;
