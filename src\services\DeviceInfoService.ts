/**
 * Device Information Service for Clipsy Android App
 * Gathers and manages device information for identification and pairing
 */

import { Platform } from 'react-native';

// Platform-specific imports
let Device: any = null;
let Network: any = null;
let AsyncStorage: any = null;

if (Platform.OS !== 'web') {
  try {
    Device = require('expo-device');
    Network = require('expo-network');
    AsyncStorage = require('@react-native-async-storage/async-storage').default;
  } catch (error) {
    console.warn('Native modules not available on this platform:', error);
  }
}

export interface DeviceInformation {
  id: string;
  name: string;
  type: 'android' | 'ios' | 'web';
  model: string;
  brand: string;
  osVersion: string;
  appVersion: string;
  networkInfo: NetworkInformation;
  capabilities: DeviceCapabilities;
  lastUpdated: string;
}

export interface NetworkInformation {
  ipAddress: string;
  networkName: string;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  isConnected: boolean;
  isInternetReachable: boolean;
}

export interface DeviceCapabilities {
  hasCamera: boolean;
  supportsBackgroundTasks: boolean;
  supportsNotifications: boolean;
  supportsClipboard: boolean;
  supportsQRScanning: boolean;
  supportsNetworkDiscovery: boolean;
}

class DeviceInfoService {
  private deviceInfo: DeviceInformation | null = null;
  private readonly DEVICE_INFO_KEY = 'clipsy_device_info';
  private readonly DEVICE_ID_KEY = 'clipsy_device_id';

  /**
   * Initialize device information service
   */
  async initialize(): Promise<DeviceInformation> {
    console.log('Initializing device information service...');

    try {
      // Load cached device info
      await this.loadCachedDeviceInfo();

      // Update device info
      this.deviceInfo = await this.gatherDeviceInformation();

      // Cache updated info
      await this.cacheDeviceInfo();

      console.log('Device information initialized:', this.deviceInfo);
      return this.deviceInfo;
    } catch (error) {
      console.error('Failed to initialize device info service:', error);

      // Create fallback device info for web
      if (Platform.OS === 'web') {
        this.deviceInfo = this.createFallbackDeviceInfo();
        return this.deviceInfo;
      }

      throw error;
    }
  }

  /**
   * Gather comprehensive device information
   */
  private async gatherDeviceInformation(): Promise<DeviceInformation> {
    const deviceId = await this.getOrCreateDeviceId();
    const networkInfo = await this.getNetworkInformation();
    const capabilities = await this.getDeviceCapabilities();

    const deviceInfo: DeviceInformation = {
      id: deviceId,
      name: await this.getDeviceName(),
      type: this.getDeviceType(),
      model: Device?.modelName || 'Unknown Model',
      brand: Device?.brand || 'Unknown Brand',
      osVersion: Device?.osVersion || 'Unknown Version',
      appVersion: '1.0.0', // This should come from app.json
      networkInfo,
      capabilities,
      lastUpdated: new Date().toISOString()
    };

    return deviceInfo;
  }

  /**
   * Create fallback device info for web platform
   */
  private createFallbackDeviceInfo(): DeviceInformation {
    return {
      id: `web_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Web Browser',
      type: 'web',
      model: 'Browser',
      brand: 'Web',
      osVersion: navigator.userAgent || 'Unknown',
      appVersion: '1.0.0',
      networkInfo: {
        ipAddress: 'Unknown',
        networkName: 'Unknown',
        connectionType: 'unknown',
        isConnected: navigator.onLine || false,
        isInternetReachable: navigator.onLine || false
      },
      capabilities: {
        hasCamera: false,
        supportsBackgroundTasks: false,
        supportsNotifications: true,
        supportsClipboard: true,
        supportsQRScanning: false,
        supportsNetworkDiscovery: false
      },
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get or create unique device ID
   */
  private async getOrCreateDeviceId(): Promise<string> {
    if (Platform.OS === 'web' || !AsyncStorage) {
      // For web, create a session-based ID
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      return `web_${timestamp}_${random}`;
    }

    try {
      let deviceId = await AsyncStorage.getItem(this.DEVICE_ID_KEY);

      if (!deviceId) {
        // Create new device ID
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        const platform = Platform.OS === 'android' ? 'android' : 'ios';
        deviceId = `${platform}_${timestamp}_${random}`;

        await AsyncStorage.setItem(this.DEVICE_ID_KEY, deviceId);
        console.log('Created new device ID:', deviceId);
      }

      return deviceId;
    } catch (error) {
      console.error('Failed to get/create device ID:', error);
      // Fallback to temporary ID
      const platform = Platform.OS === 'android' ? 'android' : 'ios';
      return `${platform}_temp_${Date.now()}`;
    }
  }

  /**
   * Get device name (user-friendly)
   */
  private async getDeviceName(): Promise<string> {
    if (Platform.OS === 'web') {
      return 'Web Browser';
    }

    try {
      // Try to get custom device name from storage
      if (AsyncStorage) {
        const customName = await AsyncStorage.getItem('clipsy_device_name');
        if (customName) {
          return customName;
        }
      }

      // Generate default name based on device info
      const brand = Device?.brand || 'Unknown';
      const model = Device?.modelName || 'Device';

      return `${brand} ${model}`;
    } catch (error) {
      console.error('Failed to get device name:', error);
      return Platform.OS === 'android' ? 'Android Device' : 'iOS Device';
    }
  }

  /**
   * Get device type
   */
  private getDeviceType(): 'android' | 'ios' | 'web' {
    if (Platform.OS === 'android') return 'android';
    if (Platform.OS === 'ios') return 'ios';
    return 'web';
  }

  /**
   * Get network information
   */
  private async getNetworkInformation(): Promise<NetworkInformation> {
    if (Platform.OS === 'web' || !Network) {
      return {
        ipAddress: 'Unknown',
        networkName: 'Unknown',
        connectionType: 'unknown',
        isConnected: navigator.onLine || false,
        isInternetReachable: navigator.onLine || false
      };
    }

    try {
      const networkState = await Network.getNetworkStateAsync();

      const networkInfo: NetworkInformation = {
        ipAddress: await this.getLocalIPAddress(),
        networkName: await this.getNetworkName(),
        connectionType: this.mapConnectionType(networkState.type),
        isConnected: networkState.isConnected || false,
        isInternetReachable: networkState.isInternetReachable || false
      };

      return networkInfo;
    } catch (error) {
      console.error('Failed to get network information:', error);
      return {
        ipAddress: 'Unknown',
        networkName: 'Unknown',
        connectionType: 'unknown',
        isConnected: false,
        isInternetReachable: false
      };
    }
  }

  /**
   * Get local IP address
   */
  private async getLocalIPAddress(): Promise<string> {
    if (Platform.OS === 'web' || !Network) {
      return 'Unknown';
    }

    try {
      const ipAddress = await Network.getIpAddressAsync();
      return ipAddress || 'Unknown';
    } catch (error) {
      console.error('Failed to get IP address:', error);
      return 'Unknown';
    }
  }

  /**
   * Get network name (WiFi SSID)
   */
  private async getNetworkName(): Promise<string> {
    try {
      // Note: Getting WiFi SSID requires additional permissions on Android
      // For now, return a generic name
      return 'Local Network';
    } catch (error) {
      console.error('Failed to get network name:', error);
      return 'Unknown Network';
    }
  }

  /**
   * Map network connection type
   */
  private mapConnectionType(networkType: any): 'wifi' | 'cellular' | 'ethernet' | 'unknown' {
    if (!Network) return 'unknown';

    switch (networkType) {
      case Network.NetworkStateType?.WIFI:
        return 'wifi';
      case Network.NetworkStateType?.CELLULAR:
        return 'cellular';
      case Network.NetworkStateType?.ETHERNET:
        return 'ethernet';
      default:
        return 'unknown';
    }
  }

  /**
   * Get device capabilities
   */
  private async getDeviceCapabilities(): Promise<DeviceCapabilities> {
    const capabilities: DeviceCapabilities = {
      hasCamera: await this.checkCameraAvailability(),
      supportsBackgroundTasks: this.checkBackgroundTaskSupport(),
      supportsNotifications: this.checkNotificationSupport(),
      supportsClipboard: this.checkClipboardSupport(),
      supportsQRScanning: await this.checkQRScanningSupport(),
      supportsNetworkDiscovery: this.checkNetworkDiscoverySupport()
    };

    return capabilities;
  }

  /**
   * Check if device has camera
   */
  private async checkCameraAvailability(): Promise<boolean> {
    try {
      return Device.hasCameraAsync ? await Device.hasCameraAsync() : true;
    } catch (error) {
      console.error('Failed to check camera availability:', error);
      return false;
    }
  }

  /**
   * Check background task support
   */
  private checkBackgroundTaskSupport(): boolean {
    // Android generally supports background tasks
    return Platform.OS === 'android';
  }

  /**
   * Check notification support
   */
  private checkNotificationSupport(): boolean {
    // Most mobile devices support notifications
    return Platform.OS === 'android' || Platform.OS === 'ios';
  }

  /**
   * Check clipboard support
   */
  private checkClipboardSupport(): boolean {
    // All platforms support clipboard
    return true;
  }

  /**
   * Check QR scanning support
   */
  private async checkQRScanningSupport(): Promise<boolean> {
    try {
      const hasCamera = await this.checkCameraAvailability();
      return hasCamera; // QR scanning requires camera
    } catch (error) {
      console.error('Failed to check QR scanning support:', error);
      return false;
    }
  }

  /**
   * Check network discovery support
   */
  private checkNetworkDiscoverySupport(): boolean {
    // Network discovery is supported on mobile platforms
    return Platform.OS === 'android' || Platform.OS === 'ios';
  }

  /**
   * Get current device information
   */
  getDeviceInfo(): DeviceInformation | null {
    return this.deviceInfo;
  }

  /**
   * Update device name
   */
  async updateDeviceName(newName: string): Promise<void> {
    try {
      await AsyncStorage.setItem('clipsy_device_name', newName);
      
      if (this.deviceInfo) {
        this.deviceInfo.name = newName;
        this.deviceInfo.lastUpdated = new Date().toISOString();
        await this.cacheDeviceInfo();
      }
      
      console.log('Device name updated to:', newName);
    } catch (error) {
      console.error('Failed to update device name:', error);
      throw error;
    }
  }

  /**
   * Refresh network information
   */
  async refreshNetworkInfo(): Promise<NetworkInformation> {
    try {
      const networkInfo = await this.getNetworkInformation();
      
      if (this.deviceInfo) {
        this.deviceInfo.networkInfo = networkInfo;
        this.deviceInfo.lastUpdated = new Date().toISOString();
        await this.cacheDeviceInfo();
      }
      
      return networkInfo;
    } catch (error) {
      console.error('Failed to refresh network info:', error);
      throw error;
    }
  }

  /**
   * Cache device information
   */
  private async cacheDeviceInfo(): Promise<void> {
    try {
      if (this.deviceInfo) {
        await AsyncStorage.setItem(this.DEVICE_INFO_KEY, JSON.stringify(this.deviceInfo));
      }
    } catch (error) {
      console.error('Failed to cache device info:', error);
    }
  }

  /**
   * Load cached device information
   */
  private async loadCachedDeviceInfo(): Promise<void> {
    try {
      const cachedInfo = await AsyncStorage.getItem(this.DEVICE_INFO_KEY);
      if (cachedInfo) {
        this.deviceInfo = JSON.parse(cachedInfo);
        console.log('Loaded cached device info');
      }
    } catch (error) {
      console.error('Failed to load cached device info:', error);
    }
  }

  /**
   * Get device info for QR code generation
   */
  getQRCodeDeviceInfo(): any {
    if (!this.deviceInfo) {
      return {
        device_name: 'Android Device',
        device_id: 'unknown',
        platform: 'android'
      };
    }

    return {
      device_name: this.deviceInfo.name,
      device_id: this.deviceInfo.id,
      platform: this.deviceInfo.type,
      model: this.deviceInfo.model,
      brand: this.deviceInfo.brand,
      version: this.deviceInfo.appVersion
    };
  }

  /**
   * Get device info for network discovery
   */
  getNetworkDiscoveryInfo(): any {
    if (!this.deviceInfo) {
      return {
        device_name: 'Android Device',
        device_id: 'unknown',
        services: ['clipsy']
      };
    }

    return {
      device_name: this.deviceInfo.name,
      device_id: this.deviceInfo.id,
      platform: this.deviceInfo.type,
      services: ['clipsy'],
      capabilities: this.deviceInfo.capabilities,
      version: this.deviceInfo.appVersion
    };
  }

  /**
   * Clear all cached device information
   */
  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([this.DEVICE_INFO_KEY, this.DEVICE_ID_KEY, 'clipsy_device_name']);
      this.deviceInfo = null;
      console.log('Device info cache cleared');
    } catch (error) {
      console.error('Failed to clear device info cache:', error);
    }
  }
}

// Export singleton instance
export const deviceInfoService = new DeviceInfoService();
export default DeviceInfoService;
