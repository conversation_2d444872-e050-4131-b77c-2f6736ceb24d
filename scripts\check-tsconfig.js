// Script to check the effective TypeScript configuration
const ts = require('typescript');
const path = require('path');

function logTsConfig() {
  try {
    const configPath = ts.findConfigFile(
      process.cwd(),
      ts.sys.fileExists,
      'tsconfig.json'
    );
    
    if (!configPath) {
      console.log('Could not find a valid tsconfig.json');
      return;
    }
    
    console.log(`Found tsconfig at: ${configPath}`);
    
    const { config, error } = ts.readConfigFile(configPath, ts.sys.readFile);
    
    if (error) {
      console.log(`Error reading tsconfig: ${error.messageText}`);
      return;
    }
    
    console.log('Raw tsconfig content:');
    console.log(JSON.stringify(config, null, 2));
    
    // Parse the config to get the effective settings after inheritance, etc.
    const parsedConfig = ts.parseJsonConfigFileContent(
      config,
      ts.sys,
      path.dirname(configPath)
    );
    
    console.log('\nEffective compiler options after inheritance:');
    console.log(JSON.stringify(parsedConfig.options, null, 2));
    
    // Specifically check moduleResolution
    console.log('\nModuleResolution setting:');
    console.log(parsedConfig.options.moduleResolution);
    
    // Check if customConditions exists
    console.log('\nCustomConditions setting:');
    console.log(parsedConfig.options.customConditions || 'Not defined');
  } catch (e) {
    console.error('Error analyzing tsconfig:', e);
  }
}

logTsConfig();
