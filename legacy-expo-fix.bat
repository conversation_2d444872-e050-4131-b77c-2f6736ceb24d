@echo off
echo Starting ClipBee mobile app with legacy compatibility mode...

:: Set environment variables to bypass TypeScript and security issues
set EXPO_NO_TYPESCRIPT_SETUP=1
set NODE_OPTIONS=--openssl-legacy-provider
set EXPO_NO_MANIFEST_SERVER=1

:: Tell user what to do
echo.
echo ============================================================
echo IMPORTANT: To fix manifest errors in Expo Go:
echo 1. Use the QR code OR enter the URL manually in Expo Go
echo 2. If issues persist, try an older version of Expo Go app
echo    (Expo Go v2.19.x or older works best with Expo SDK 48)
echo 3. Make sure your device and computer are on the same network
echo ============================================================
echo.

:: Start with tunnel option to bypass local network issues
echo Starting with tunnel connection (most reliable)...
npx expo-cli start --no-dev --tunnel
