import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  ScrollView
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import MobileWebSocketServer, { 
  ConnectionInfo, 
  ServerStatus, 
  ConnectedDevice 
} from '../services/MobileWebSocketServer';

interface MobileQRGeneratorProps {
  onDeviceConnected?: (device: ConnectedDevice) => void;
  onBack: () => void;
}

const MobileQRGenerator: React.FC<MobileQRGeneratorProps> = ({ onDeviceConnected, onBack }) => {
  const [serverStatus, setServerStatus] = useState<ServerStatus>('stopped');
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo | null>(null);
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>([]);
  const [qrData, setQrData] = useState<string>('');

  useEffect(() => {
    // Set up WebSocket server callbacks
    MobileWebSocketServer.setCallbacks({
      onStatusChange: (status) => {
        setServerStatus(status);
        if (status === 'running') {
          const info = MobileWebSocketServer.getConnectionInfo();
          setConnectionInfo(info);
          if (info) {
            generateQRData(info);
          }
        } else {
          setConnectionInfo(null);
          setQrData('');
        }
      },
      onDeviceConnected: (device) => {
        setConnectedDevices(prev => [...prev, device]);
        onDeviceConnected?.(device);
        Alert.alert(
          '📱 Device Connected',
          `${device.device_name} has connected successfully!`,
          [{ text: 'OK' }]
        );
      },
      onDeviceDisconnected: (deviceId) => {
        setConnectedDevices(prev => prev.filter(d => d.device_id !== deviceId));
      },
      onClipboardReceived: (clipboard) => {
        console.log('Received clipboard from connected device:', clipboard.content);
      },
      onError: (error) => {
        Alert.alert('Server Error', error, [{ text: 'OK' }]);
      },
    });

    // Get initial status
    setServerStatus(MobileWebSocketServer.getStatus());
    setConnectedDevices(MobileWebSocketServer.getConnectedDevices());

    return () => {
      // Cleanup if needed
    };
  }, []);

  const generateQRData = (info: ConnectionInfo) => {
    const qrPayload = {
      type: 'clipsync_pairing',
      ip: info.ip,
      port: info.port,
      token: info.token,
      device_id: info.device_id,
      device_name: info.device_name,
      device_type: info.device_type,
      timestamp: Date.now(),
    };
    
    setQrData(JSON.stringify(qrPayload));
  };

  const handleStartServer = async () => {
    const success = await MobileWebSocketServer.startServer(8080);
    if (!success) {
      Alert.alert(
        'Server Start Failed',
        'Could not start the pairing server. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleStopServer = async () => {
    await MobileWebSocketServer.stopServer();
  };

  const getStatusColor = (status: ServerStatus): string => {
    switch (status) {
      case 'running': return '#4CAF50';
      case 'starting': return '#FF9800';
      case 'error': return '#f44336';
      default: return '#757575';
    }
  };

  const getStatusText = (status: ServerStatus): string => {
    switch (status) {
      case 'running': return '🟢 Server Running';
      case 'starting': return '🟡 Starting Server...';
      case 'error': return '🔴 Server Error';
      default: return '⚪ Server Stopped';
    }
  };

  const { width } = Dimensions.get('window');
  const qrSize = Math.min(width * 0.6, 200);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>📱 Generate QR Code</Text>
      </View>

      <View style={styles.content}>
        {/* Server Status */}
        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>Server Status</Text>
          <View style={[styles.statusCard, { borderColor: getStatusColor(serverStatus) }]}>
            <Text style={[styles.statusText, { color: getStatusColor(serverStatus) }]}>
              {getStatusText(serverStatus)}
            </Text>
            {connectionInfo && (
              <View style={styles.connectionDetails}>
                <Text style={styles.detailText}>IP: {connectionInfo.ip}</Text>
                <Text style={styles.detailText}>Port: {connectionInfo.port}</Text>
                <Text style={styles.detailText}>Device: {connectionInfo.device_name}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Server Controls */}
        <View style={styles.controlsSection}>
          <Text style={styles.sectionTitle}>Server Controls</Text>
          
          {serverStatus === 'stopped' && (
            <TouchableOpacity 
              style={styles.startButton} 
              onPress={handleStartServer}
            >
              <Text style={styles.startButtonText}>🚀 Start Pairing Server</Text>
            </TouchableOpacity>
          )}

          {serverStatus === 'running' && (
            <TouchableOpacity 
              style={styles.stopButton} 
              onPress={handleStopServer}
            >
              <Text style={styles.stopButtonText}>🛑 Stop Server</Text>
            </TouchableOpacity>
          )}

          {serverStatus === 'starting' && (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Starting server...</Text>
            </View>
          )}
        </View>

        {/* QR Code Display */}
        {serverStatus === 'running' && qrData && (
          <View style={styles.qrSection}>
            <Text style={styles.sectionTitle}>QR Code for Pairing</Text>
            <View style={styles.qrContainer}>
              <QRCode
                value={qrData}
                size={qrSize}
                backgroundColor="white"
                color="black"
              />
            </View>
            <Text style={styles.qrInstructions}>
              Other devices can scan this QR code to connect to this device
            </Text>
          </View>
        )}

        {/* Connected Devices */}
        <View style={styles.devicesSection}>
          <Text style={styles.sectionTitle}>
            Connected Devices ({connectedDevices.length})
          </Text>
          
          {connectedDevices.length === 0 ? (
            <View style={styles.emptyDevices}>
              <Text style={styles.emptyDevicesText}>
                No devices connected yet
              </Text>
              <Text style={styles.emptyDevicesSubtext}>
                Share the QR code above for other devices to connect
              </Text>
            </View>
          ) : (
            connectedDevices.map((device) => (
              <View key={device.device_id} style={styles.deviceItem}>
                <View style={styles.deviceInfo}>
                  <Text style={styles.deviceName}>📱 {device.device_name}</Text>
                  <Text style={styles.deviceDetails}>
                    Connected: {new Date(device.connected_at).toLocaleTimeString()}
                  </Text>
                  {device.current_clipboard && (
                    <Text style={styles.deviceClipboard} numberOfLines={2}>
                      Last clipboard: {device.current_clipboard}
                    </Text>
                  )}
                </View>
                <View style={styles.deviceStatus}>
                  <View style={styles.onlineIndicator} />
                  <Text style={styles.onlineText}>Online</Text>
                </View>
              </View>
            ))
          )}
        </View>

        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={styles.sectionTitle}>📋 How It Works</Text>
          <View style={styles.instructionCard}>
            <Text style={styles.instructionText}>
              1. Tap "Start Pairing Server" to begin hosting
            </Text>
            <Text style={styles.instructionText}>
              2. A QR code will appear with connection details
            </Text>
            <Text style={styles.instructionText}>
              3. Other devices can scan this QR code to connect
            </Text>
            <Text style={styles.instructionText}>
              4. Once connected, clipboard content will sync automatically
            </Text>
            <Text style={styles.instructionText}>
              5. This enables Android-to-Android connections
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: '500',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e0e0e0',
  },
  content: {
    padding: 20,
  },
  statusSection: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e0e0e0',
    marginBottom: 15,
  },
  statusCard: {
    backgroundColor: '#1E1E1E',
    padding: 20,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  connectionDetails: {
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: '#a0a0a0',
    marginBottom: 2,
    fontFamily: 'monospace',
  },
  controlsSection: {
    marginBottom: 25,
  },
  startButton: {
    backgroundColor: '#4CAF50',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  stopButton: {
    backgroundColor: '#f44336',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  stopButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#a0a0a0',
  },
  qrSection: {
    marginBottom: 25,
    alignItems: 'center',
  },
  qrContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
  },
  qrInstructions: {
    fontSize: 14,
    color: '#a0a0a0',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  devicesSection: {
    marginBottom: 25,
  },
  emptyDevices: {
    backgroundColor: '#1E1E1E',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  emptyDevicesText: {
    fontSize: 16,
    color: '#a0a0a0',
    marginBottom: 5,
  },
  emptyDevicesSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  deviceItem: {
    backgroundColor: '#1E1E1E',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e0e0e0',
    marginBottom: 5,
  },
  deviceDetails: {
    fontSize: 12,
    color: '#a0a0a0',
    marginBottom: 3,
  },
  deviceClipboard: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  deviceStatus: {
    alignItems: 'center',
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginBottom: 2,
  },
  onlineText: {
    fontSize: 10,
    color: '#4CAF50',
  },
  instructionsSection: {
    marginBottom: 25,
  },
  instructionCard: {
    backgroundColor: '#1E1E1E',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  instructionText: {
    fontSize: 14,
    color: '#e0e0e0',
    marginBottom: 8,
    lineHeight: 20,
  },
});

export default MobileQRGenerator;
