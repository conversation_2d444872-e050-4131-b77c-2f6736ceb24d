/**
 * Simple HTTP server for serving the web app
 */
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = process.env.PORT || 4000;
const WEB_BUILD_DIR = path.join(__dirname, 'web-build');

// Map file extensions to MIME types
const MIME_TYPES = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.ttf': 'font/ttf',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.eot': 'font/eot',
};

// Create HTTP server
const server = http.createServer((req, res) => {
  try {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // Parse URL to get the path
    let filePath = req.url;
    
    // Default to index.html if root path
    if (filePath === '/' || filePath === '') {
      filePath = '/index.html';
    }
    
    // Handle browser preview requests
    if (filePath.includes('vscodeBrowserReqId')) {
      filePath = '/index.html';
    }

    // Resolve the file path
    const resolvedPath = path.join(WEB_BUILD_DIR, filePath);
    
    // Get file extension
    const ext = path.extname(resolvedPath);
    
    // Get content type based on file extension
    const contentType = MIME_TYPES[ext] || 'application/octet-stream';
  
    // Check if file exists
    fs.access(resolvedPath, fs.constants.F_OK, (err) => {
      if (err) {
        // If file doesn't exist, serve index.html for SPA routing
        const indexPath = path.join(WEB_BUILD_DIR, 'index.html');
        
        fs.readFile(indexPath, (err, content) => {
          if (err) {
            res.writeHead(500);
            res.end(`Error loading index.html: ${err.code}`);
            return;
          }
          
          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(content, 'utf-8');
        });
        return;
      }
      
      // Read the file
      fs.readFile(resolvedPath, (err, content) => {
        if (err) {
          res.writeHead(500);
          res.end(`Error loading ${filePath}: ${err.code}`);
          return;
        }
        
        // Serve the file
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content, 'utf-8');
      });
    });
  } catch (error) {
    console.error('Error handling request:', error);
    res.writeHead(500);
    res.end('Internal server error');
  }
});

// Handle server errors to prevent termination
server.on('error', (err) => {
  console.error('Server error:', err);
  
  if (err.code === 'EADDRINUSE') {
    console.log(`Port ${PORT} is already in use. Trying again in 5 seconds...`);
    setTimeout(() => {
      server.close();
      server.listen(PORT);
    }, 5000);
  }
});

// Keep the process running even if there's an uncaught exception
process.on('uncaughtException', (err) => {
  console.error('Uncaught exception:', err);
  console.log('Server will continue running...');
});

// Start server
server.listen(PORT, () => {
  console.log(`
  ┌─────────────────────────────────────────┐
  │                                         │
  │   ClipBee Web Server                    │
  │                                         │
  │   - Local:    http://localhost:${PORT}     │
  │                                         │
  │   Web app is now available!             │
  │                                         │
  └─────────────────────────────────────────┘
  `);
});

// Keep the server alive by holding the main thread open
process.stdin.resume();

// Handle process termination gracefully
process.on('SIGINT', () => {
  console.log('\nShutting down server gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

// Periodically check server status
setInterval(() => {
  console.log('Server heartbeat check: running on port ' + PORT);
}, 30000);
