// Set environment variables to bypass common issues
process.env.EXPO_NO_TYPESCRIPT_SETUP = true;
process.env.NODE_OPTIONS = '--openssl-legacy-provider'; // Help with Node 17+ compatibility
process.env.EXPO_DEBUG = 'true'; // More verbose logging

// Start Expo with spawn
const { spawn } = require('child_process');
const path = require('path');

// Get command line arguments and filter out the node and script path
const args = process.argv.slice(2);

// Use the legacy expo-cli command that was working previously
// Based on previous successful run, we use the --no-dev flag
const expoArgs = ['expo-cli', 'start', '--no-dev'];

// Add any additional arguments passed to this script
if (args.length > 0) {
  expoArgs.push(...args);
}

console.log('Starting Expo with legacy CLI...');

const expo = spawn('npx', expoArgs, {
  stdio: 'inherit',
  shell: true
});

expo.on('error', (err) => {
  console.error('Failed to start Expo:', err);
  process.exit(1);
});

expo.on('close', (code) => {
  process.exit(code);
});
