/**
 * Web-optimized entry point for ClipBee
 */
import React, { lazy, Suspense } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Platform } from 'react-native';

// Dynamically import the main App component to reduce initial bundle size
const MainApp = lazy(() => import('./App'));

// Simple loading component
const LoadingScreen = () => (
  <View style={styles.container}>
    <ActivityIndicator size="large" color="#0066CC" />
    <Text style={styles.text}>Loading ClipBee...</Text>
  </View>
);

/**
 * Web-optimized app wrapper that uses code splitting
 * This component is only used for web builds
 */
const AppWeb = () => {
  // Only use code splitting on web platform
  if (Platform.OS === 'web') {
    return (
      <Suspense fallback={<LoadingScreen />}>
        <MainApp />
      </Suspense>
    );
  }
  
  // On native platforms, import directly
  const App = require('./App').default;
  return <App />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  text: {
    color: '#E0E0E0',
    marginTop: 16,
    fontSize: 18,
  }
});

export default AppWeb;
