import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
} from 'react-native';

export default function SimpleClippyApp() {
  console.log('SimpleClippyApp: Component rendering');

  const [message, setMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showQR, setShowQR] = useState(false);
  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Android App Version 1! This is your Android device clipboard content. You can edit this content and it will sync with connected devices.');
  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Desktop PC. You can edit this content and it will be sent to the connected device.');
  const [isEditingThis, setIsEditingThis] = useState(false);
  const [isEditingConnected, setIsEditingConnected] = useState(false);
  const [editTextThis, setEditTextThis] = useState('');
  const [editTextConnected, setEditTextConnected] = useState('');

  const [pairedDevices] = useState([
    { id: '1', name: 'Desktop PC - Office', status: 'connected', lastSeen: '2 min ago' },
    { id: '2', name: 'MacBook Pro - Home', status: 'disconnected', lastSeen: '1 hour ago' }
  ]);

  const showMessage = (text: string) => {
    setMessage(text);
    setTimeout(() => setMessage(''), 3000);
  };

  const startEditThis = () => {
    setEditTextThis(thisDeviceClipboard);
    setIsEditingThis(true);
  };

  const saveEditThis = () => {
    setThisDeviceClipboard(editTextThis);
    setIsEditingThis(false);
    showMessage('✅ This Device clipboard updated!');
  };

  const startEditConnected = () => {
    setEditTextConnected(connectedDeviceClipboard);
    setIsEditingConnected(true);
  };

  const saveEditConnected = () => {
    setConnectedDeviceClipboard(editTextConnected);
    setIsEditingConnected(false);
    showMessage('✅ Connected Device clipboard updated!');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#201c1c" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.logoContainer}>
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>📱</Text>
            </View>
            <View style={[
              styles.connectionDot,
              { backgroundColor: pairedDevices.some(d => d.status === 'connected') ? '#4CAF50' : '#F44336' }
            ]} />
          </View>
          <Text style={styles.title}>Clipsy</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton} onPress={() => setShowSettings(true)}>
            <Text style={styles.iconButtonText}>⚙️</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={() => showMessage('Minimized!')}>
            <Text style={styles.iconButtonText}>➖</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={() => showMessage('Pinned!')}>
            <Text style={styles.iconButtonText}>📌</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Success Message */}
      {message ? (
        <View style={styles.successMessage}>
          <Text style={styles.successMessageText}>{message}</Text>
        </View>
      ) : null}

      {/* Main Content */}
      <ScrollView style={styles.content}>
        {/* This Device Section */}
        <View style={styles.deviceSection}>
          <View style={styles.deviceHeader}>
            <Text style={styles.deviceTitle}>📱 This Device</Text>
            <Text style={styles.deviceSubtitle}>Android App Version 1</Text>
          </View>
          {isEditingThis ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.editInput}
                value={editTextThis}
                onChangeText={setEditTextThis}
                multiline
                placeholder="Enter clipboard content..."
                placeholderTextColor="#666"
              />
              <View style={styles.editButtons}>
                <TouchableOpacity style={styles.saveButton} onPress={saveEditThis}>
                  <Text style={styles.buttonText}>Save</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={() => setIsEditingThis(false)}>
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.clipboardContent}>
              <Text style={styles.clipboardText}>{thisDeviceClipboard}</Text>
              <TouchableOpacity style={styles.editButton} onPress={startEditThis}>
                <Text style={styles.editButtonText}>✏️ Edit</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Connected Device Section */}
        <View style={styles.deviceSection}>
          <View style={styles.deviceHeader}>
            <Text style={styles.deviceTitle}>🔗 Connected Device</Text>
            <Text style={styles.deviceSubtitle}>Desktop PC - Office 🟢</Text>
          </View>
          {isEditingConnected ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.editInput}
                value={editTextConnected}
                onChangeText={setEditTextConnected}
                multiline
                placeholder="Enter clipboard content..."
                placeholderTextColor="#666"
              />
              <View style={styles.editButtons}>
                <TouchableOpacity style={styles.saveButton} onPress={saveEditConnected}>
                  <Text style={styles.buttonText}>Save</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={() => setIsEditingConnected(false)}>
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.clipboardContent}>
              <Text style={styles.clipboardText}>{connectedDeviceClipboard}</Text>
              <TouchableOpacity style={styles.editButton} onPress={startEditConnected}>
                <Text style={styles.editButtonText}>✏️ Edit</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Clipboard History */}
        <Text style={styles.historyTitle}>Clipboard History</Text>
        <View style={styles.historyList}>
          <TouchableOpacity style={styles.historyItem} onPress={() => showMessage('History item 1 selected!')}>
            <Text style={styles.timestamp}>2 minutes ago</Text>
            <Text style={styles.itemContent}>This is an older clipboard item. It's shorter.</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.historyItem} onPress={() => showMessage('History item 2 selected!')}>
            <Text style={styles.timestamp}>10 minutes ago</Text>
            <Text style={styles.itemContent}>Another item from history. This one might be a bit longer.</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.historyItem} onPress={() => showMessage('History item 3 selected!')}>
            <Text style={styles.timestamp}>1 hour ago</Text>
            <Text style={styles.itemContent}>Yet another historical entry.</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Floating Sync Button */}
      <TouchableOpacity style={styles.floatingSyncButton} onPress={() => showMessage('Sync completed!')}>
        <Text style={styles.syncButtonText}>🔄</Text>
      </TouchableOpacity>

      {/* Settings Modal */}
      <Modal visible={showSettings} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.settingsModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Settings</Text>
              <TouchableOpacity onPress={() => setShowSettings(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <Text style={styles.sectionTitle}>Paired Devices</Text>
              {pairedDevices.map(device => (
                <View key={device.id} style={styles.deviceItem}>
                  <View style={styles.deviceInfo}>
                    <Text style={styles.deviceName}>{device.name}</Text>
                    <Text style={styles.deviceStatus}>{device.status} • {device.lastSeen}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.deviceButton}
                    onPress={() => showMessage(`${device.status === 'connected' ? 'Disconnected' : 'Connected'} ${device.name}`)}
                  >
                    <Text style={styles.deviceButtonText}>
                      {device.status === 'connected' ? 'Disconnect' : 'Connect'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}

              <TouchableOpacity style={styles.qrButton} onPress={() => setShowQR(true)}>
                <Text style={styles.qrButtonText}>Generate QR Code</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* QR Modal */}
      <Modal visible={showQR} animationType="fade" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.qrModal}>
            <Text style={styles.qrTitle}>QR Code for Pairing</Text>
            <View style={styles.qrPlaceholder}>
              <Text style={styles.qrText}>📱 QR CODE 📱</Text>
            </View>
            <Text style={styles.qrInstructions}>Scan this QR code with another device to pair</Text>
            <TouchableOpacity style={styles.closeQRButton} onPress={() => setShowQR(false)}>
              <Text style={styles.closeQRButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#201c1c',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#201c1c',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 0,
  },
  logoContainer: {
    position: 'relative',
    marginRight: 0,
  },
  logoPlaceholder: {
    width: 32,
    height: 32,
    backgroundColor: '#333',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontSize: 20,
  },
  connectionDot: {
    position: 'absolute',
    bottom: -2,
    left: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#201c1c',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  iconButton: {
    padding: 8,
    borderRadius: 4,
  },
  iconButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  successMessage: {
    backgroundColor: '#4CAF50',
    padding: 12,
    marginHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  successMessageText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  deviceSection: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginBottom: 8,
  },
  deviceHeader: {
    marginBottom: 12,
  },
  deviceTitle: {
    color: '#E0E0E0',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  deviceSubtitle: {
    color: '#A0A0A0',
    fontSize: 14,
  },
  clipboardContent: {
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
  },
  clipboardText: {
    color: '#E0E0E0',
    fontSize: 16,
    marginBottom: 8,
    lineHeight: 22,
  },
  editButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#8B5CF6',
    borderRadius: 6,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  editContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 12,
  },
  editInput: {
    backgroundColor: '#333',
    color: '#FFFFFF',
    padding: 12,
    borderRadius: 6,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  editButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
  },
  cancelButton: {
    backgroundColor: '#666',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
  },
  buttonText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#A0A0A0',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  historyList: {
    marginHorizontal: 16,
    marginBottom: 100,
  },
  historyItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  timestamp: {
    fontSize: 12,
    color: '#A0A0A0',
    marginBottom: 4,
  },
  itemContent: {
    color: '#E0E0E0',
    fontSize: 14,
    lineHeight: 18,
  },
  floatingSyncButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#2a2a2a',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 6,
  },
  syncButtonText: {
    fontSize: 24,
    color: '#8B5CF6',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsModal: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    color: '#FFFFFF',
    fontSize: 18,
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  sectionTitle: {
    color: '#A0A0A0',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  deviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#333',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  deviceStatus: {
    color: '#A0A0A0',
    fontSize: 12,
  },
  deviceButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  deviceButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  qrButton: {
    backgroundColor: '#8B5CF6',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  qrButtonText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  qrModal: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '80%',
  },
  qrTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  qrPlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: '#333',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  qrText: {
    color: '#A0A0A0',
    fontSize: 16,
  },
  qrInstructions: {
    color: '#A0A0A0',
    textAlign: 'center',
    marginBottom: 20,
  },
  closeQRButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  closeQRButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
