{"name": "@clipsync/mobile", "version": "1.2.0", "main": "index.js", "scripts": {"start": "expo start -c", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web -c", "eject": "expo eject", "postinstall": "npx jetifier -r", "reset-cache": "expo start -c"}, "dependencies": {"@clipsync/common": "0.1.0", "@expo/metro-config": "~0.17.0", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/netinfo": "^11.1.0", "@types/uuid": "^10.0.0", "expo": "~50.0.0", "expo-asset": "~9.0.2", "expo-background-fetch": "~11.8.0", "expo-barcode-scanner": "^12.9.3", "expo-constants": "~15.4.6", "expo-crypto": "~12.8.0", "expo-device": "~5.9.0", "expo-font": "~11.10.0", "expo-linking": "~6.2.0", "expo-network": "^5.8.0", "expo-permissions": "^14.4.0", "expo-router": "~3.4.10", "expo-splash-screen": "~0.26.0", "expo-sqlite": "~13.4.0", "expo-status-bar": "~1.11.0", "expo-task-manager": "~11.7.0", "jetifier": "^2.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-device-info": "^10.14.0", "react-native-gesture-handler": "~2.14.0", "react-native-network-info": "^5.2.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "^14.1.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "~0.19.6", "react-native-webview": "^13.6.4", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.23.5", "@expo/metro-runtime": "~3.1.3", "@playwright/test": "^1.53.1", "@types/jest": "^29.5.10", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "@types/react-test-renderer": "^18.0.0", "babel-plugin-module-resolver": "^5.0.0", "cross-env": "^7.0.3", "jest": "^29.7.0", "jest-expo": "~50.0.1", "metro": "^0.80.0", "metro-babel-transformer": "^0.80.0", "metro-cache": "^0.80.0", "metro-config": "^0.80.0", "metro-core": "^0.80.0", "metro-react-native-babel-preset": "^0.77.0", "metro-resolver": "^0.80.0", "metro-runtime": "^0.80.0", "metro-source-map": "^0.80.0", "metro-transform-worker": "^0.80.0", "playwright": "^1.53.1", "react-test-renderer": "18.2.0", "typescript": "~5.3.3", "webpack-dev-server": "^5.2.2"}, "private": true}