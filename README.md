# Clipsy Mobile - React Native Clipboard Manager

A cross-platform mobile application built with React Native and Expo for managing clipboard content across devices.

## Features

- 📋 Clipboard content management
- 🔄 Real-time synchronization across devices
- 📱 Cross-platform support (iOS & Android)
- 🔗 QR code sharing
- 🌐 Web interface integration
- 🔒 Secure data handling

## Tech Stack

- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and tools
- **TypeScript** - Type-safe JavaScript
- **React Navigation** - Navigation library
- **WebSocket** - Real-time communication
- **SQLite** - Local data storage

## Project Structure

```
mobile/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # Screen components
│   └── services/       # Business logic and API services
├── android/            # Android-specific files
├── assets/             # Images, fonts, and other assets
├── ClipBeeClean/       # Clean Expo template
└── utils/              # Utility functions
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Lastt0ne/Clipsy-mobile.git
cd Clipsy-mobile
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
# or
expo start
```

### Running on Device

#### Android
```bash
npm run android
# or
expo run:android
```

#### iOS
```bash
npm run ios
# or
expo run:ios
```

### Web Development
```bash
npm run web
# or
expo start --web
```

## Available Scripts

- `npm start` - Start the Expo development server
- `npm run android` - Run on Android device/emulator
- `npm run ios` - Run on iOS device/simulator
- `npm run web` - Run in web browser
- `npm test` - Run tests
- `npm run build` - Build for production

## Configuration

The app can be configured through:
- `app.json` - Expo configuration
- `metro.config.js` - Metro bundler configuration
- `babel.config.js` - Babel transpiler configuration

## Development

### Code Structure

- **Components**: Reusable UI components in `/src/components/`
- **Screens**: Full-screen components in `/src/screens/`
- **Services**: Business logic and API calls in `/src/services/`
- **Utils**: Helper functions and utilities in `/utils/`

### Key Components

- `DemoClipsyInterface.tsx` - Main clipboard interface
- `QRCodeScanner.tsx` - QR code scanning functionality
- `MobileQRGenerator.tsx` - QR code generation
- `ClipboardService.ts` - Clipboard management service
- `WebSocketService.ts` - Real-time communication

## Building for Production

### Android APK
```bash
expo build:android
```

### iOS IPA
```bash
expo build:ios
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue on GitHub.

## Roadmap

- [ ] Enhanced UI/UX improvements
- [ ] Cloud synchronization
- [ ] Advanced clipboard history
- [ ] File sharing capabilities
- [ ] End-to-end encryption
- [ ] Desktop companion app integration

---

Built with ❤️ using React Native and Expo
