import React from 'react';
import { Text, StyleSheet } from 'react-native';

// This is a fallback component for when MaterialIcons font fails to load
interface MaterialIconProps {
  name: string;
  size?: number;
  color?: string;
  style?: any;
}

// Map of common icon names to unicode fallbacks
const ICON_MAP: Record<string, string> = {
  'delete-outline': '🗑️',
  'delete': '🗑️',
  'edit': '✏️',
  'content-copy': '📋',
  'content_copy': '📋',
  'content-paste': '📄',
  'content_paste': '📄',
  'sync': '🔄',
  'settings': '⚙️',
  'brightness_4': '🌓',
};

const MaterialIcon: React.FC<MaterialIconProps> = ({ name, size = 24, color = '#000', style }) => {
  // Try to use the icon map, or fallback to a generic icon
  const iconText = ICON_MAP[name] || '•';
  
  return (
    <Text 
      style={[
        styles.icon, 
        { fontSize: size, color: color },
        style
      ]}
    >
      {iconText}
    </Text>
  );
};

const styles = StyleSheet.create({
  icon: {
    textAlign: 'center',
  }
});

export default MaterialIcon; 