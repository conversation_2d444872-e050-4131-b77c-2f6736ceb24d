module.exports = function(api) {
  api.cache(true);
  
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          alias: {
            '@common': '../common',
            '@components': './components',
            '@screens': './screens',
            '@utils': './utils',
          }
        }
      ],
      'react-native-reanimated/plugin',
      '@babel/plugin-syntax-dynamic-import',
      
      // Additional production optimizations
      ...(isProduction ? [
        ['transform-remove-console', { exclude: ['error', 'warn'] }],
        'transform-react-remove-prop-types'
      ] : [])
    ]
  };
};