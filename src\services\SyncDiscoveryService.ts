/**
 * Sync Discovery Service for Cross-Platform Device Discovery
 * Discovers Android and Windows devices for clipboard synchronization
 */

import { Platform } from 'react-native';
import { SyncDevice, DevicePlatform, crossPlatformSyncProtocol } from './CrossPlatformSyncProtocol';

export interface DiscoveryConfiguration {
  scanInterval: number; // milliseconds
  timeout: number; // milliseconds
  maxDevices: number;
  enableAndroidDiscovery: boolean;
  enableWindowsDiscovery: boolean;
  autoConnect: boolean;
  preferredPorts: number[];
  broadcastPort: number;
}

export interface DiscoveryResult {
  devices: SyncDevice[];
  scanDuration: number;
  timestamp: number;
  errors: string[];
}

export class SyncDiscoveryService {
  private config: DiscoveryConfiguration;
  private isScanning: boolean = false;
  private discoveredDevices: Map<string, SyncDevice> = new Map();
  private scanTimer: NodeJS.Timeout | null = null;
  private callbacks: {
    onDeviceFound?: (device: SyncDevice) => void;
    onDeviceLost?: (device: SyncDevice) => void;
    onScanComplete?: (result: DiscoveryResult) => void;
    onError?: (error: string) => void;
  } = {};

  constructor(config: Partial<DiscoveryConfiguration> = {}) {
    this.config = {
      scanInterval: 10000, // 10 seconds
      timeout: 5000, // 5 seconds
      maxDevices: 10,
      enableAndroidDiscovery: true,
      enableWindowsDiscovery: true,
      autoConnect: false,
      preferredPorts: [8080, 8081, 8082, 9090],
      broadcastPort: 8888,
      ...config
    };
  }

  /**
   * Initialize discovery service
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    this.callbacks = callbacks;
    console.log('Sync discovery service initialized');
    
    // Start periodic scanning if enabled
    if (this.config.scanInterval > 0) {
      this.startPeriodicScanning();
    }
  }

  /**
   * Start device discovery scan
   */
  async startDiscovery(): Promise<DiscoveryResult> {
    if (this.isScanning) {
      throw new Error('Discovery already in progress');
    }

    this.isScanning = true;
    const startTime = Date.now();
    const errors: string[] = [];
    const foundDevices: SyncDevice[] = [];

    try {
      console.log('Starting cross-platform device discovery...');

      // Discover Android devices
      if (this.config.enableAndroidDiscovery) {
        try {
          const androidDevices = await this.discoverAndroidDevices();
          foundDevices.push(...androidDevices);
        } catch (error) {
          errors.push(`Android discovery failed: ${error}`);
        }
      }

      // Discover Windows devices
      if (this.config.enableWindowsDiscovery) {
        try {
          const windowsDevices = await this.discoverWindowsDevices();
          foundDevices.push(...windowsDevices);
        } catch (error) {
          errors.push(`Windows discovery failed: ${error}`);
        }
      }

      // Update discovered devices map
      foundDevices.forEach(device => {
        this.discoveredDevices.set(device.id, device);
        this.callbacks.onDeviceFound?.(device);
      });

      const result: DiscoveryResult = {
        devices: foundDevices,
        scanDuration: Date.now() - startTime,
        timestamp: Date.now(),
        errors
      };

      this.callbacks.onScanComplete?.(result);
      return result;

    } finally {
      this.isScanning = false;
    }
  }

  /**
   * Discover Android devices on the network
   */
  private async discoverAndroidDevices(): Promise<SyncDevice[]> {
    console.log('Discovering Android devices...');
    
    // Simulate Android device discovery
    const mockAndroidDevices: SyncDevice[] = [
      {
        id: 'android-phone-001',
        name: 'Samsung Galaxy S23',
        platform: 'android',
        ip: '*************',
        port: 8080,
        version: '1.0.0',
        capabilities: {
          supportsClipboard: true,
          supportsFiles: false,
          supportsImages: true,
          supportsRichText: false,
          supportsEncryption: true,
          maxClipboardSize: 1024 * 1024, // 1MB
          supportedFormats: ['text', 'html', 'image']
        },
        lastSeen: Date.now(),
        status: 'connected'
      },
      {
        id: 'android-tablet-001',
        name: 'Android Tablet',
        platform: 'android',
        ip: '*************',
        port: 8081,
        version: '1.0.0',
        capabilities: {
          supportsClipboard: true,
          supportsFiles: false,
          supportsImages: true,
          supportsRichText: false,
          supportsEncryption: true,
          maxClipboardSize: 2 * 1024 * 1024, // 2MB
          supportedFormats: ['text', 'html', 'image']
        },
        lastSeen: Date.now(),
        status: 'connected'
      }
    ];

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

    // Filter out current device if it's Android
    const currentDeviceId = Platform.OS === 'android' ? 'android-phone-001' : null;
    return mockAndroidDevices.filter(device => device.id !== currentDeviceId);
  }

  /**
   * Discover Windows devices on the network
   */
  private async discoverWindowsDevices(): Promise<SyncDevice[]> {
    console.log('Discovering Windows devices...');
    
    // Simulate Windows device discovery
    const mockWindowsDevices: SyncDevice[] = [
      {
        id: 'windows-desktop-001',
        name: 'Desktop PC - Office',
        platform: 'windows',
        ip: '*************',
        port: 8080,
        version: '1.0.0',
        capabilities: {
          supportsClipboard: true,
          supportsFiles: true,
          supportsImages: true,
          supportsRichText: true,
          supportsEncryption: true,
          maxClipboardSize: 10 * 1024 * 1024, // 10MB
          supportedFormats: ['text', 'html', 'rtf', 'image', 'file']
        },
        lastSeen: Date.now(),
        status: 'connected'
      },
      {
        id: 'windows-laptop-001',
        name: 'Laptop - Home',
        platform: 'windows',
        ip: '*************',
        port: 8082,
        version: '1.0.0',
        capabilities: {
          supportsClipboard: true,
          supportsFiles: true,
          supportsImages: true,
          supportsRichText: true,
          supportsEncryption: true,
          maxClipboardSize: 5 * 1024 * 1024, // 5MB
          supportedFormats: ['text', 'html', 'rtf', 'image', 'file']
        },
        lastSeen: Date.now(),
        status: 'connected'
      },
      {
        id: 'windows-workstation-001',
        name: 'Workstation - Dev',
        platform: 'windows',
        ip: '*************',
        port: 9090,
        version: '1.0.0',
        capabilities: {
          supportsClipboard: true,
          supportsFiles: true,
          supportsImages: true,
          supportsRichText: true,
          supportsEncryption: true,
          maxClipboardSize: 20 * 1024 * 1024, // 20MB
          supportedFormats: ['text', 'html', 'rtf', 'image', 'file']
        },
        lastSeen: Date.now(),
        status: 'connected'
      }
    ];

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

    return mockWindowsDevices;
  }

  /**
   * Get devices by platform
   */
  getDevicesByPlatform(platform: DevicePlatform): SyncDevice[] {
    return Array.from(this.discoveredDevices.values())
      .filter(device => device.platform === platform);
  }

  /**
   * Get Android devices
   */
  getAndroidDevices(): SyncDevice[] {
    return this.getDevicesByPlatform('android');
  }

  /**
   * Get Windows devices
   */
  getWindowsDevices(): SyncDevice[] {
    return this.getDevicesByPlatform('windows');
  }

  /**
   * Get all discovered devices
   */
  getAllDevices(): SyncDevice[] {
    return Array.from(this.discoveredDevices.values());
  }

  /**
   * Get device by ID
   */
  getDevice(deviceId: string): SyncDevice | undefined {
    return this.discoveredDevices.get(deviceId);
  }

  /**
   * Connect to a discovered device
   */
  async connectToDevice(deviceId: string): Promise<boolean> {
    const device = this.discoveredDevices.get(deviceId);
    if (!device) {
      throw new Error(`Device not found: ${deviceId}`);
    }

    try {
      console.log(`Connecting to ${device.name} (${device.platform})...`);
      
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Update device status
      device.status = 'connected';
      device.lastSeen = Date.now();
      
      // Add to sync protocol
      crossPlatformSyncProtocol.addConnectedDevice(device);
      
      console.log(`Successfully connected to ${device.name}`);
      return true;
      
    } catch (error) {
      console.error(`Failed to connect to ${device.name}:`, error);
      device.status = 'error';
      this.callbacks.onError?.(`Connection failed: ${error}`);
      return false;
    }
  }

  /**
   * Disconnect from a device
   */
  async disconnectFromDevice(deviceId: string): Promise<boolean> {
    const device = this.discoveredDevices.get(deviceId);
    if (!device) {
      return false;
    }

    try {
      console.log(`Disconnecting from ${device.name}...`);
      
      // Update device status
      device.status = 'disconnected';
      
      // Remove from sync protocol
      crossPlatformSyncProtocol.removeConnectedDevice(deviceId);
      
      console.log(`Disconnected from ${device.name}`);
      return true;
      
    } catch (error) {
      console.error(`Failed to disconnect from ${device.name}:`, error);
      this.callbacks.onError?.(`Disconnection failed: ${error}`);
      return false;
    }
  }

  /**
   * Start periodic scanning
   */
  private startPeriodicScanning(): void {
    if (this.scanTimer) {
      clearInterval(this.scanTimer);
    }

    this.scanTimer = setInterval(async () => {
      if (!this.isScanning) {
        try {
          await this.startDiscovery();
        } catch (error) {
          console.error('Periodic scan failed:', error);
        }
      }
    }, this.config.scanInterval);

    console.log(`Periodic scanning started (interval: ${this.config.scanInterval}ms)`);
  }

  /**
   * Stop periodic scanning
   */
  stopPeriodicScanning(): void {
    if (this.scanTimer) {
      clearInterval(this.scanTimer);
      this.scanTimer = null;
      console.log('Periodic scanning stopped');
    }
  }

  /**
   * Stop discovery service
   */
  async stop(): Promise<void> {
    this.stopPeriodicScanning();
    this.isScanning = false;
    
    // Disconnect from all devices
    const devices = Array.from(this.discoveredDevices.keys());
    for (const deviceId of devices) {
      await this.disconnectFromDevice(deviceId);
    }
    
    this.discoveredDevices.clear();
    console.log('Sync discovery service stopped');
  }

  /**
   * Update discovery configuration
   */
  updateConfiguration(newConfig: Partial<DiscoveryConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart periodic scanning if interval changed
    if (newConfig.scanInterval !== undefined) {
      this.stopPeriodicScanning();
      if (this.config.scanInterval > 0) {
        this.startPeriodicScanning();
      }
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): DiscoveryConfiguration {
    return { ...this.config };
  }

  /**
   * Get discovery statistics
   */
  getDiscoveryStats(): {
    totalDevices: number;
    androidDevices: number;
    windowsDevices: number;
    connectedDevices: number;
    lastScanTime: number;
  } {
    const devices = this.getAllDevices();
    return {
      totalDevices: devices.length,
      androidDevices: this.getAndroidDevices().length,
      windowsDevices: this.getWindowsDevices().length,
      connectedDevices: devices.filter(d => d.status === 'connected').length,
      lastScanTime: Math.max(...devices.map(d => d.lastSeen), 0)
    };
  }
}

// Export singleton instance
export const syncDiscoveryService = new SyncDiscoveryService();
export default SyncDiscoveryService;
