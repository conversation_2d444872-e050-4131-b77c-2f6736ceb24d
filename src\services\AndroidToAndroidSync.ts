/**
 * Android to Android Sync Service
 * Handles peer-to-peer clipboard synchronization between Android devices
 */

import { Platform } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { ClipboardSyncItem, SyncDevice, crossPlatformSyncProtocol } from './CrossPlatformSyncProtocol';
import { syncDiscoveryService } from './SyncDiscoveryService';

export interface AndroidSyncConfiguration {
  enablePeerToPeer: boolean;
  maxClipboardSize: number; // bytes
  syncInterval: number; // milliseconds
  retryAttempts: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  autoSyncOnChange: boolean;
  allowedDeviceTypes: string[];
}

export interface AndroidSyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  bytesTransferred: number;
  averageLatency: number;
  lastSyncTime: number;
  connectedAndroidDevices: number;
}

export class AndroidToAndroidSync {
  private config: AndroidSyncConfiguration;
  private stats: AndroidSyncStats;
  private isInitialized: boolean = false;
  private syncTimer: NodeJS.Timeout | null = null;
  private lastClipboardContent: string = '';
  private callbacks: {
    onSyncSuccess?: (targetDevice: SyncDevice, item: ClipboardSyncItem) => void;
    onSyncFailure?: (targetDevice: SyncDevice, error: string) => void;
    onClipboardChange?: (content: string) => void;
    onDeviceConnected?: (device: SyncDevice) => void;
    onDeviceDisconnected?: (device: SyncDevice) => void;
  } = {};

  constructor(config: Partial<AndroidSyncConfiguration> = {}) {
    this.config = {
      enablePeerToPeer: true,
      maxClipboardSize: 1024 * 1024, // 1MB limit for Android
      syncInterval: 3000, // 3 seconds
      retryAttempts: 3,
      compressionEnabled: true,
      encryptionEnabled: false, // Simplified for demo
      autoSyncOnChange: true,
      allowedDeviceTypes: ['phone', 'tablet'],
      ...config
    };

    this.stats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      bytesTransferred: 0,
      averageLatency: 0,
      lastSyncTime: 0,
      connectedAndroidDevices: 0
    };
  }

  /**
   * Initialize Android to Android sync
   */
  async initialize(callbacks: typeof this.callbacks): Promise<void> {
    if (Platform.OS !== 'android') {
      throw new Error('Android to Android sync is only available on Android devices');
    }

    this.callbacks = callbacks;
    this.isInitialized = true;

    // Start clipboard monitoring
    if (this.config.autoSyncOnChange) {
      this.startClipboardMonitoring();
    }

    // Start periodic sync
    this.startPeriodicSync();

    console.log('Android to Android sync initialized');
  }

  /**
   * Sync clipboard content to all connected Android devices
   */
  async syncToAllAndroidDevices(content?: string): Promise<boolean[]> {
    if (!this.isInitialized) {
      throw new Error('Android sync not initialized');
    }

    const clipboardContent = content || await this.getCurrentClipboardContent();
    if (!clipboardContent || clipboardContent.trim() === '') {
      console.log('No clipboard content to sync');
      return [];
    }

    // Validate content size
    if (clipboardContent.length > this.config.maxClipboardSize) {
      throw new Error(`Clipboard content too large (${clipboardContent.length} bytes, max ${this.config.maxClipboardSize})`);
    }

    const androidDevices = syncDiscoveryService.getAndroidDevices()
      .filter(device => device.status === 'connected');

    if (androidDevices.length === 0) {
      console.log('No connected Android devices found');
      return [];
    }

    const syncItem = this.createClipboardSyncItem(clipboardContent);
    const results: boolean[] = [];

    console.log(`Syncing to ${androidDevices.length} Android devices...`);

    for (const device of androidDevices) {
      try {
        const success = await this.syncToAndroidDevice(device, syncItem);
        results.push(success);
        
        if (success) {
          this.callbacks.onSyncSuccess?.(device, syncItem);
        }
      } catch (error) {
        console.error(`Failed to sync to ${device.name}:`, error);
        results.push(false);
        this.callbacks.onSyncFailure?.(device, error.toString());
      }
    }

    return results;
  }

  /**
   * Sync to a specific Android device
   */
  async syncToAndroidDevice(targetDevice: SyncDevice, syncItem: ClipboardSyncItem): Promise<boolean> {
    if (targetDevice.platform !== 'android') {
      throw new Error('Target device is not an Android device');
    }

    const startTime = Date.now();

    try {
      console.log(`Syncing to Android device: ${targetDevice.name}`);

      // Validate device capabilities
      if (!targetDevice.capabilities.supportsClipboard) {
        throw new Error('Target device does not support clipboard sync');
      }

      if (syncItem.size > targetDevice.capabilities.maxClipboardSize) {
        throw new Error(`Content too large for target device (${syncItem.size} > ${targetDevice.capabilities.maxClipboardSize})`);
      }

      // Perform Android-specific optimizations
      const optimizedItem = this.optimizeForAndroid(syncItem);

      // Use the cross-platform protocol for actual sync
      const success = await crossPlatformSyncProtocol.syncAndroidToAndroid(targetDevice, optimizedItem);

      if (success) {
        this.updateStats('success', Date.now() - startTime, optimizedItem.size);
        console.log(`Successfully synced to ${targetDevice.name}`);
      } else {
        this.updateStats('failure', Date.now() - startTime, 0);
      }

      return success;

    } catch (error) {
      this.updateStats('failure', Date.now() - startTime, 0);
      throw error;
    }
  }

  /**
   * Handle incoming sync from another Android device
   */
  async handleIncomingAndroidSync(sourceDevice: SyncDevice, syncItem: ClipboardSyncItem): Promise<boolean> {
    try {
      console.log(`Receiving sync from Android device: ${sourceDevice.name}`);

      // Validate incoming content
      if (syncItem.size > this.config.maxClipboardSize) {
        throw new Error('Incoming content too large');
      }

      // Apply Android-specific processing
      const processedContent = this.processIncomingAndroidContent(syncItem);

      // Update local clipboard
      await Clipboard.setStringAsync(processedContent);
      this.lastClipboardContent = processedContent;

      // Notify callback
      this.callbacks.onClipboardChange?.(processedContent);

      console.log(`Successfully received sync from ${sourceDevice.name}`);
      return true;

    } catch (error) {
      console.error(`Failed to handle incoming sync from ${sourceDevice.name}:`, error);
      return false;
    }
  }

  /**
   * Start clipboard monitoring for auto-sync
   */
  private startClipboardMonitoring(): void {
    const monitorClipboard = async () => {
      try {
        const currentContent = await this.getCurrentClipboardContent();
        
        if (currentContent !== this.lastClipboardContent && currentContent.trim() !== '') {
          console.log('Clipboard changed, triggering auto-sync...');
          this.lastClipboardContent = currentContent;
          
          // Trigger sync to all Android devices
          await this.syncToAllAndroidDevices(currentContent);
        }
      } catch (error) {
        console.error('Clipboard monitoring error:', error);
      }
    };

    // Monitor clipboard every 2 seconds
    setInterval(monitorClipboard, 2000);
    console.log('Clipboard monitoring started');
  }

  /**
   * Start periodic sync
   */
  private startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      try {
        const androidDevices = syncDiscoveryService.getAndroidDevices()
          .filter(device => device.status === 'connected');

        if (androidDevices.length > 0) {
          await this.syncToAllAndroidDevices();
        }
      } catch (error) {
        console.error('Periodic sync error:', error);
      }
    }, this.config.syncInterval);

    console.log(`Periodic Android sync started (interval: ${this.config.syncInterval}ms)`);
  }

  /**
   * Get current clipboard content
   */
  private async getCurrentClipboardContent(): Promise<string> {
    try {
      return await Clipboard.getStringAsync();
    } catch (error) {
      console.error('Failed to get clipboard content:', error);
      return '';
    }
  }

  /**
   * Create clipboard sync item
   */
  private createClipboardSyncItem(content: string): ClipboardSyncItem {
    return {
      id: `android-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      format: 'text',
      timestamp: Date.now(),
      sourceDevice: 'current-android-device',
      sourcePlatform: 'android',
      size: content.length,
      checksum: this.generateChecksum(content),
      metadata: {
        title: 'Android Clipboard Content',
        description: 'Synced from Android device',
        priority: 'normal'
      }
    };
  }

  /**
   * Optimize sync item for Android devices
   */
  private optimizeForAndroid(syncItem: ClipboardSyncItem): ClipboardSyncItem {
    let optimizedContent = syncItem.content;

    // Remove excessive whitespace
    optimizedContent = optimizedContent.trim().replace(/\s+/g, ' ');

    // Ensure content is within Android limits
    if (optimizedContent.length > this.config.maxClipboardSize) {
      optimizedContent = optimizedContent.substring(0, this.config.maxClipboardSize - 100) + '... [truncated]';
    }

    return {
      ...syncItem,
      content: optimizedContent,
      size: optimizedContent.length,
      checksum: this.generateChecksum(optimizedContent),
      metadata: {
        ...syncItem.metadata,
        optimizedForAndroid: true,
        originalSize: syncItem.size
      }
    };
  }

  /**
   * Process incoming content from another Android device
   */
  private processIncomingAndroidContent(syncItem: ClipboardSyncItem): string {
    let content = syncItem.content;

    // Android-specific content processing
    // Remove any platform-specific formatting
    content = content.replace(/\r\n/g, '\n'); // Normalize line endings
    content = content.trim();

    return content;
  }

  /**
   * Generate simple checksum for content validation
   */
  private generateChecksum(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Update sync statistics
   */
  private updateStats(result: 'success' | 'failure', latency: number, bytes: number): void {
    this.stats.totalSyncs++;
    
    if (result === 'success') {
      this.stats.successfulSyncs++;
      this.stats.bytesTransferred += bytes;
      
      // Update average latency
      const totalLatency = this.stats.averageLatency * (this.stats.successfulSyncs - 1) + latency;
      this.stats.averageLatency = totalLatency / this.stats.successfulSyncs;
    } else {
      this.stats.failedSyncs++;
    }
    
    this.stats.lastSyncTime = Date.now();
    this.stats.connectedAndroidDevices = syncDiscoveryService.getAndroidDevices()
      .filter(device => device.status === 'connected').length;
  }

  /**
   * Get sync statistics
   */
  getSyncStats(): AndroidSyncStats {
    return { ...this.stats };
  }

  /**
   * Get connected Android devices
   */
  getConnectedAndroidDevices(): SyncDevice[] {
    return syncDiscoveryService.getAndroidDevices()
      .filter(device => device.status === 'connected');
  }

  /**
   * Update configuration
   */
  updateConfiguration(newConfig: Partial<AndroidSyncConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart periodic sync if interval changed
    if (newConfig.syncInterval !== undefined) {
      this.startPeriodicSync();
    }
  }

  /**
   * Stop Android to Android sync
   */
  async stop(): Promise<void> {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    
    this.isInitialized = false;
    console.log('Android to Android sync stopped');
  }

  /**
   * Force sync now
   */
  async forceSyncNow(): Promise<boolean[]> {
    console.log('Force syncing to all Android devices...');
    return await this.syncToAllAndroidDevices();
  }
}

// Export singleton instance
export const androidToAndroidSync = new AndroidToAndroidSync();
export default AndroidToAndroidSync;
