import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Platform,
  Alert
} from 'react-native';
import ClipboardService from '../services/ClipboardService';

const ClipboardTestScreen: React.FC = () => {
  const [isMonitoring, setIsMonitoring] = useState<boolean>(false);
  const [clipboardItems, setClipboardItems] = useState<Array<{content: string, timestamp: Date}>>([]);
  const [customText, setCustomText] = useState<string>('');
  const [status, setStatus] = useState<string>('Clipboard monitoring is inactive');

  // Effect to load clipboard history on component mount
  useEffect(() => {
    loadClipboardHistory();
    
    // Check if monitoring is already active
    const monitoringActive = ClipboardService.isClipboardMonitoringActive();
    setIsMonitoring(monitoringActive);
    
    if (monitoringActive) {
      setStatus('Clipboard monitoring is active');
    }
    
    // Set up polling to refresh clipboard history
    const intervalId = setInterval(() => {
      if (isMonitoring) {
        loadClipboardHistory();
      }
    }, 2000);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [isMonitoring]);

  const loadClipboardHistory = async () => {
    try {
      const history = await ClipboardService.getHistory(10);
      setClipboardItems(history.map(item => ({
        content: item.content,
        timestamp: item.timestamp
      })));
    } catch (error) {
      console.error('Failed to load clipboard history:', error);
      setStatus('Failed to load clipboard history');
    }
  };

  const toggleMonitoring = () => {
    if (isMonitoring) {
      ClipboardService.stopMonitoring();
      setStatus('Clipboard monitoring stopped');
    } else {
      ClipboardService.startMonitoring(1000);
      setStatus('Clipboard monitoring started');
    }
    setIsMonitoring(!isMonitoring);
  };

  const copyCustomText = () => {
    if (customText.trim() === '') {
      Alert.alert('Error', 'Please enter some text to copy');
      return;
    }
    
    ClipboardService.setClipboardText(customText)
      .then(() => {
        setStatus(`Copied text: "${customText}"`);
        if (Platform.OS === 'android') {
          Alert.alert(
            'Text Copied',
            'Text copied to clipboard. If monitoring is active, it should appear in the list shortly.',
            [{ text: 'OK' }]
          );
        }
      })
      .catch(error => {
        console.error('Failed to copy text:', error);
        setStatus('Failed to copy text to clipboard');
      });
  };

  const formatDate = (date: Date): string => {
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Clipboard Monitor Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>Status: {status}</Text>
        <Text style={styles.deviceId}>Device ID: {ClipboardService.getDeviceId()}</Text>
      </View>
      
      <View style={styles.controlsContainer}>
        <TouchableOpacity 
          style={[styles.button, isMonitoring ? styles.stopButton : styles.startButton]} 
          onPress={toggleMonitoring}
        >
          <Text style={styles.buttonText}>
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Text>
        </TouchableOpacity>
        
        <View style={styles.copyContainer}>
          <TextInput
            style={styles.input}
            value={customText}
            onChangeText={setCustomText}
            placeholder="Enter text to copy"
            placeholderTextColor="#999"
          />
          <TouchableOpacity style={styles.copyButton} onPress={copyCustomText}>
            <Text style={styles.buttonText}>Copy</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.sectionTitle}>Test Instructions:</Text>
      <Text style={styles.instructions}>
        1. Start monitoring{'\n'}
        2. Copy text from another app{'\n'}
        3. Return to this app{'\n'}
        4. See if the copied text appears below
      </Text>
      
      <Text style={styles.sectionTitle}>Recent Clipboard Items:</Text>
      <ScrollView style={styles.historyContainer}>
        {clipboardItems.length > 0 ? (
          clipboardItems.map((item, index) => (
            <View key={index} style={styles.historyItem}>
              <Text style={styles.historyContent} numberOfLines={2}>
                {item.content}
              </Text>
              <Text style={styles.historyTimestamp}>
                {formatDate(item.timestamp)}
              </Text>
            </View>
          ))
        ) : (
          <Text style={styles.emptyHistory}>No clipboard history found</Text>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#121212',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: '#1E1E1E',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  deviceId: {
    color: '#AAAAAA',
    fontSize: 12,
    marginTop: 4,
  },
  controlsContainer: {
    marginBottom: 16,
  },
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  startButton: {
    backgroundColor: '#2196F3',
  },
  stopButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  copyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    backgroundColor: '#2A2A2A',
    color: '#FFFFFF',
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  copyButton: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  instructions: {
    color: '#CCCCCC',
    marginBottom: 16,
    lineHeight: 20,
  },
  historyContainer: {
    flex: 1,
    backgroundColor: '#1E1E1E',
    borderRadius: 8,
  },
  historyItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  historyContent: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  historyTimestamp: {
    color: '#AAAAAA',
    fontSize: 12,
    marginTop: 4,
  },
  emptyHistory: {
    color: '#999999',
    padding: 16,
    textAlign: 'center',
  },
});

export default ClipboardTestScreen;
