const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const CompressionPlugin = require('compression-webpack-plugin');

module.exports = async function(env, argv) {
  // Get the default Expo webpack config
  const config = await createExpoWebpackConfigAsync(env, argv);
  
  // Check if we're in production mode
  const isProduction = env.mode === 'production';
  
  if (isProduction) {
    // Optimize bundle splitting
    if (config.optimization && config.optimization.splitChunks) {
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 20000,
        maxSize: 500000, // Try to keep chunks under 500KB
        cacheGroups: {
          defaultVendors: false,
          default: false,
          reactVendor: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-native|react-native-web)[\\/]/,
            name: 'react-vendor',
            chunks: 'all',
            priority: 30,
          },
          expoVendor: {
            test: /[\\/]node_modules[\\/](expo|@expo)[\\/]/,
            name: 'expo-vendor',
            chunks: 'all',
            priority: 20,
          },
          libraryVendor: {
            test: /[\\/]node_modules[\\/](!react)(!react-dom)(!react-native)(!react-native-web)(!expo)(!@expo)[\\/]/,
            name: 'lib-vendor',
            chunks: 'all',
            priority: 10,
          },
          utilityVendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'util-vendor',
            chunks: 'all',
            priority: 5,
          },
        }
      };
    }
    
    // Add CompressionPlugin to create gzipped versions of assets
    config.plugins.push(
      new CompressionPlugin({
        test: /\.(js|css|html|svg)$/,
        algorithm: 'gzip',
        threshold: 10240,
        minRatio: 0.8
      })
    );
    
    // Optimize TerserPlugin settings
    if (config.optimization && config.optimization.minimizer) {
      const terserPluginIndex = config.optimization.minimizer.findIndex(
        plugin => plugin.constructor.name === 'TerserPlugin'
      );
      
      if (terserPluginIndex !== -1) {
        const terserPlugin = config.optimization.minimizer[terserPluginIndex];
        if (terserPlugin.options) {
          terserPlugin.options.terserOptions = {
            ...terserPlugin.options.terserOptions,
            compress: {
              ...terserPlugin.options.terserOptions?.compress,
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.debug', 'console.info']
            },
            output: {
              ...terserPlugin.options.terserOptions?.output,
              comments: false
            }
          };
        }
      }
    }
  }
  
  return config;
};
