@echo off
echo Starting ClipBee for Android with Expo Go...
echo.
echo Make sure:
echo 1. You have the Expo Go app installed on your Android device
echo 2. Your device is connected to the same network as this computer
echo 3. No other Expo servers are running
echo.

:: Clear Metro bundler cache to avoid manifest issues
echo Clearing Metro cache...
if exist node_modules\.cache\metro rmdir /s /q node_modules\.cache\metro

:: Set environment variables that worked in previous sessions
set EXPO_NO_TYPESCRIPT_SETUP=1
set NODE_OPTIONS=--openssl-legacy-provider

:: Use the legacy expo-cli that worked for you previously
echo Starting Expo server - this may take a moment...
call npx expo-cli start --no-dev --clear

echo.
echo If you see "Failed to construct manifest" error in Expo Go:
echo 1. Make sure your phone and computer are on the same network
echo 2. Try entering the URL manually in Expo Go (exp://YOUR-IP-ADDRESS:19000)
