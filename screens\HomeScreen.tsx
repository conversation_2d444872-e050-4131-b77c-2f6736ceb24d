import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  SafeAreaView,
  StatusBar,
  FlatList,
  RefreshControl,
} from 'react-native';

// Import the OptimizedIconLoader instead of OptimizedIcon directly
import OptimizedIconLoader from '../components/OptimizedIconLoader';
import Clipboard from '@react-native-clipboard/clipboard';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types
interface HomeScreenProps {
  navigation: any;
  route: any;
}

interface ClipboardItem {
  id: string;
  text: string;
  timestamp: number;
  content?: string;
  deviceId?: string;
  contentType?: string;
  [key: string]: any;
}

interface StyleType {
  [key: string]: ViewStyle | TextStyle | undefined;
  container?: {
    flex: number;
    backgroundColor: string;
  };
  header?: {
    padding: number;
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    backgroundColor: string;
  };
  headerLeft?: {
    flexDirection: 'row';
    alignItems: 'center';
  };
  headerRight?: {
    flexDirection: 'row';
  };
  title?: {
    color: string;
    fontSize: number;
    fontWeight: '600' | '500' | 'normal' | 'bold' | '100' | '200' | '300' | '400' | '700' | '800' | '900';
    marginLeft: number;
  };
}

const styles: StyleType = StyleSheet.create({
  container: {
    flex: 1 as const,
    backgroundColor: '#121212'
  },
  header: {
    padding: 16,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    backgroundColor: '#121212'
  },
  headerLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const
  },
  headerRight: {
    flexDirection: 'row' as const
  },
  title: {
    color: '#E0E0E0',
    fontSize: 24,
    fontWeight: '600' as const,
    marginLeft: 8
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8
  },
  connected: {
    backgroundColor: '#4CAF50'
  },
  disconnected: {
    backgroundColor: '#F44336'
  },
  iconBtn: {
    padding: 8,
    borderRadius: 50,
    backgroundColor: 'transparent'
  },
  iconBtnHover: {
    backgroundColor: '#333333'
  },
  actionBtn: {
    padding: 8,
    borderRadius: 50,
    backgroundColor: 'transparent'
  },
  actionBtnHover: {
    backgroundColor: '#333333'
  },
  mainContent: {
    flex: 1,
    padding: 16
  },
  livePreview: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    minHeight: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4
  },
  livePreviewText: {
    color: '#E0E0E0',
    fontSize: 16,
    lineHeight: 24
  },
  actionIconGroup: {
    flexDirection: 'row' as const,
    marginTop: 12,
    alignSelf: 'flex-end' as const
  },
  historySection: {
    marginBottom: 24
  },
  historyTitle: {
    color: '#A0A0A0',
    fontSize: 18,
    fontWeight: '500' as const,
    marginBottom: 12
  },
  historyList: {
    alignItems: 'stretch' as const
  },
  historyItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 2
  },
  timestamp: {
    color: '#A0A0A0',
    fontSize: 14
  },
  content: {
    color: '#E0E0E0',
    fontSize: 16,
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis' as const
  },
  contentExpanded: {
    flex: 1
  },
  bottomBar: {
    backgroundColor: '#1E1E1E',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#333333'
  },
  syncBtn: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#2D3748',
    padding: 12,
    borderRadius: 20
  },
  syncBtnText: {
    color: '#E0E0E0',
    fontSize: 16,
    marginLeft: 8
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20
  },
  emptyStateText: {
    color: '#A0A0A0',
    fontSize: 16,
    textAlign: 'center' as const
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const
  },
  loadingText: {
    marginTop: 16,
    color: '#E0E0E0',
    fontSize: 16
  }
});


const HomeScreen = ({ navigation, route }: HomeScreenProps) => {
  const [clipboardItems, setClipboardItems] = useState<ClipboardItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSyncing, setIsSyncing] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [currentClipboardText, setCurrentClipboardText] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ClipboardItem | null>(null);

  useEffect(() => {
    const loadAndListen = async () => {
      try {
        await loadClipboardHistory();
        const currentText = await Clipboard.getString();
        if (currentText) {
          setCurrentClipboardText(currentText);
        }
      } catch (err) {
        console.error('Error loading clipboard:', err);
      }
    };

    loadAndListen();

    const subscription = Clipboard.addListener(async () => {
      try {
        const text = await Clipboard.getString();
        setCurrentClipboardText(text);
        await saveToHistory(text);
      } catch (err) {
        console.error('Error handling clipboard change:', err);
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const loadClipboardHistory = async () => {
    try {
      setLoading(true);
      const history = await AsyncStorage.getItem('clipboardHistory');
      if (history) {
        const items = JSON.parse(history) as ClipboardItem[];
        setClipboardItems(items);
      }
    } catch (err) {
      console.error('Error loading clipboard history:', err);
      setError('Failed to load clipboard history');
    } finally {
      setLoading(false);
    }
  };

  const saveToHistory = async (text: string) => {
    try {
      const timestamp = Date.now();
      const newItem: ClipboardItem = {
        id: timestamp.toString(),
        text,
        timestamp,
      };
      const updatedItems = [newItem, ...clipboardItems].slice(0, 100);
      setClipboardItems(updatedItems);
      await AsyncStorage.setItem('clipboardHistory', JSON.stringify(updatedItems));
    } catch (err) {
      console.error('Error saving to history:', err);
      setError('Failed to save to history');
    }
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>No clipboard history yet</Text>
    </View>
  );

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#FFFFFF" />
      <Text style={styles.loadingText}>Loading...</Text>
    </View>
  );

  const renderError = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>{error}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#121212" />

      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.statusDot}>
            <View style={isConnected ? styles.connected as ViewStyle : styles.disconnected as ViewStyle} />
          </View>
          <Text style={styles.title}>ClipBee</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.iconBtn}
            onPress={() => {
              // Handle settings
            }}
          >
            <OptimizedIconLoader name="settings" size={24} color="#E0E0E0" fallbackText="⚙️" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.mainContent}>
        {loading ? (
          renderLoading()
        ) : error ? (
          renderError()
        ) : (
          <>
            <View style={styles.livePreview}>
              <View style={styles.livePreviewTextContainer}>
                <Text style={styles.livePreviewText} numberOfLines={3}>
                  {currentClipboardText || 'No content'}
                </Text>
              </View>
              <View style={styles.actionIconGroup}>
                <TouchableOpacity
                  style={styles.iconBtn}
                  onPress={() => {
                    // Handle copy to clipboard
                  }}
                >
                  <OptimizedIconLoader name="content-copy" size={24} color="#E0E0E0" fallbackText="📋" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.iconBtn}
                  onPress={() => {
                    // Handle delete live clipboard
                  }}
                >
                  <OptimizedIconLoader name="delete" size={24} color="#E0E0E0" fallbackText="🗑️" />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.historySection}>
              {clipboardItems.length === 0 ? (
                renderEmptyState()
              ) : (
                <FlatList
                  data={clipboardItems}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <View style={styles.clipboardItem as ViewStyle}>
                      <View style={styles.itemContent as ViewStyle}>
                        <Text style={styles.itemText as TextStyle}>{item.text}</Text>
                        <Text style={styles.itemTimestamp as TextStyle}>
                          {new Date(item.timestamp).toLocaleTimeString()}
                        </Text>
                      </View>
                      <View style={styles.itemActions as ViewStyle}>
                        <TouchableOpacity
                          style={styles.actionBtn}
                          onPress={() => {
                            // Handle item press
                          }}
                        >
                          <OptimizedIconLoader name="content-copy" size={24} color="#E0E0E0" fallbackText="📋" />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.actionBtn}
                          onPress={() => {
                            // Handle item delete
                          }}
                        >
                          <OptimizedIconLoader name="delete" size={24} color="#E0E0E0" fallbackText="🗑️" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                />
              )}
            </View>
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

export default HomeScreen;