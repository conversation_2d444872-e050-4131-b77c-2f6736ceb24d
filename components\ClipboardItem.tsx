import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ClipboardItem as ClipboardItemType } from '../common/types';
import MaterialIconPackage from 'react-native-vector-icons/MaterialIcons';
import MaterialIconFallback from './MaterialIcon';

// Use fallback icon if needed
const Icon = MaterialIconPackage || MaterialIconFallback;

interface ClipboardItemProps {
  item: ClipboardItemType;
  onPress: (item: ClipboardItemType) => void;
  onDelete: (id: string) => void;
  timeSince: string;
}

const ClipboardItemComponent: React.FC<ClipboardItemProps> = ({ 
  item, 
  onPress, 
  onDelete,
  timeSince 
}) => {
  const [expanded, setExpanded] = useState(false);
  
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={() => {
        toggleExpanded();
        onPress(item);
      }}
      activeOpacity={0.7}
    >
      <View style={styles.contentContainer}>
        <Text style={styles.timestamp}>{timeSince}</Text>
        <Text 
          style={[styles.content, expanded && styles.expandedContent]} 
          numberOfLines={expanded ? undefined : 1}
        >
          {item.content}
        </Text>
      </View>
      <TouchableOpacity 
        style={styles.deleteButton}
        onPress={(e) => {
          e.stopPropagation();
          onDelete(item.id);
        }}
      >
        <Icon name="delete-outline" size={20} color="#A0A0A0" />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#A0A0A0',
    marginBottom: 4,
    fontWeight: '400',
  },
  content: {
    fontSize: 14,
    color: '#E0E0E0',
    overflow: 'hidden',
    lineHeight: 20,
  },
  expandedContent: {
    maxHeight: undefined,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ClipboardItemComponent; 