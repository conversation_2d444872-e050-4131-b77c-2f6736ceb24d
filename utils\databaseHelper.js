/**
 * Database helper utility
 * Provides platform-specific database initialization and access
 */
import * as SQLite from 'expo-sqlite';
import { Platform } from 'react-native';
import { DATABASE_NAME, SCHEMA } from '../app/database/schema';

// Placeholder function for web that doesn't break the app
const createDummyTransaction = (callback) => {
  const dummyTx = {
    executeSql: (query, params, successCallback) => {
      console.log('Web dummy SQL execution:', query);
      // Call success callback with empty results
      if (successCallback) {
        successCallback(dummyTx, { rows: { _array: [], length: 0 } });
      }
      return true;
    }
  };
  
  callback(dummyTx);
  return true;
};

// Create dummy db for web that mimics the SQLite interface
const createWebDummyDB = () => ({
  transaction: createDummyTransaction,
  exec: (queries, readOnly, callback) => {
    console.log('Web dummy DB exec:', queries);
    if (callback) callback([]);
    return Promise.resolve([]);
  },
  close: () => {
    console.log('Web dummy DB closed');
    return Promise.resolve();
  }
});

/**
 * Opens the database based on platform
 */
export const openDatabase = () => {
  if (Platform.OS === 'web') {
    console.log('Using web dummy database implementation');
    return createWebDummyDB();
  } else {
    console.log('Using native SQLite implementation');
    return SQLite.openDatabase(DATABASE_NAME);
  }
};

/**
 * Initialize the database
 */
export const initializeDatabase = async () => {
  console.log('Initializing database for platform:', Platform.OS);
  
  try {
    const db = openDatabase();
    
    if (Platform.OS === 'web') {
      console.log('Web platform: Database initialization simulated');
      return db; // Return dummy DB for web
    }
    
    // Initialize actual database on mobile platforms
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        SCHEMA.forEach(query => {
          tx.executeSql(query);
        });
      }, 
      (error) => {
        console.error('Database initialization error:', error);
        reject(error);
      },
      () => {
        console.log('Database initialized successfully');
        resolve(db);
      });
    });
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

/**
 * Execute a query
 */
export const executeQuery = async (query, params = []) => {
  const db = openDatabase();
  
  if (Platform.OS === 'web') {
    console.log('Web platform: Query execution simulated', query, params);
    return { rows: { _array: [] } }; // Return empty results on web
  }
  
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        query, 
        params,
        (_, results) => {
          resolve(results);
        },
        (_, error) => {
          console.error('Query execution error:', error);
          reject(error);
          return false;
        }
      );
    });
  });
};
