export const DATABASE_NAME = 'clipsync.db';

export const SCHEMA = [
  `
  CREATE TABLE IF NOT EXISTS devices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    platform TEXT NOT NULL,
    last_seen INTEGER NOT NULL
  );
  `,
  `
  CREATE TABLE IF NOT EXISTS clipboard_items (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    device_id TEXT NOT NULL,
    content_type TEXT NOT NULL,
    FOREIGN KEY (device_id) REFERENCES devices (id)
  );
  `,
  `
  CREATE INDEX IF NOT EXISTS idx_clipboard_timestamp ON clipboard_items (timestamp);
  `
]; 