/**
 * Clipsy Loading Screen Component
 * Beautiful animated loading screen for app initialization
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Animated,
  Dimensions,
  StyleSheet,
  Easing,
} from 'react-native';

// Safe imports with fallbacks
let LinearGradient: any = View;
let Ionicons: any = null;

try {
  LinearGradient = require('expo-linear-gradient').LinearGradient;
} catch (error) {
  console.warn('expo-linear-gradient not available, using fallback');
}

try {
  Ionicons = require('@expo/vector-icons').Ionicons;
} catch (error) {
  console.warn('@expo/vector-icons not available, using fallback');
}

const { width, height } = Dimensions.get('window');

interface ClipsyLoadingScreenProps {
  onLoadingComplete?: () => void;
  loadingDuration?: number;
}

const WaveAnimation: React.FC = () => {
  const waveAnimations = useRef([
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
  ]).current;

  useEffect(() => {
    const createWaveAnimation = (animValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 2,
            duration: 600,
            delay,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 1,
            duration: 600,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animations = waveAnimations.map((anim, index) =>
      createWaveAnimation(anim, index * 100)
    );

    Animated.parallel(animations).start();
  }, []);

  return (
    <View style={styles.waveContainer}>
      {waveAnimations.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.waveBar,
            {
              transform: [{ scaleY: anim }],
              backgroundColor: LinearGradient === View ? '#60A5FA' : undefined,
            },
          ]}
        >
          {LinearGradient !== View ? (
            <LinearGradient
              colors={['#60A5FA', '#22D3EE']}
              style={styles.waveGradient}
            />
          ) : null}
        </Animated.View>
      ))}
    </View>
  );
};

const BackgroundGradient: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 5000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.1,
          duration: 2500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 2500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    Animated.parallel([rotateAnimation, scaleAnimation]).start();
  }, []);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.gradientContainer}>
      <Animated.View
        style={[
          styles.gradientBackground,
          {
            transform: [{ rotate }, { scale: scaleAnim }],
            backgroundColor: LinearGradient === View ? '#7B61FF' : undefined,
          },
        ]}
      >
        {LinearGradient !== View ? (
          <LinearGradient
            colors={['#00CCB1', '#7B61FF', '#FFC414', '#1CA0FB']}
            style={styles.gradient}
          />
        ) : null}
      </Animated.View>
      <View style={styles.gradientContent}>{children}</View>
    </View>
  );
};

const ClipsyLoadingScreen: React.FC<ClipsyLoadingScreenProps> = ({
  onLoadingComplete,
  loadingDuration = 3000,
}) => {
  const [loadingProgress, setLoadingProgress] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const textGlowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Initial fade in animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.back(1.2)),
        useNativeDriver: true,
      }),
    ]).start();

    // Icon rotation animation
    Animated.loop(
      Animated.timing(iconRotateAnim, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();

    // Text glow animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(textGlowAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(textGlowAnim, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    ).start();

    // Progress animation
    const progressInterval = setInterval(() => {
      setLoadingProgress((prev) => {
        const newProgress = prev + 2;
        if (newProgress >= 100) {
          clearInterval(progressInterval);
          setTimeout(() => {
            onLoadingComplete?.();
          }, 500);
          return 100;
        }
        return newProgress;
      });
    }, loadingDuration / 50);

    return () => clearInterval(progressInterval);
  }, []);

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: loadingProgress,
      duration: 100,
      easing: Easing.out(Easing.ease),
      useNativeDriver: false,
    }).start();
  }, [loadingProgress]);

  const iconRotate = iconRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const textShadowOpacity = textGlowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 1],
  });

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 100],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  const ContainerComponent = LinearGradient !== View ? LinearGradient : View;
  const containerProps = LinearGradient !== View
    ? { colors: ['#0F172A', '#581C87', '#0F172A'] }
    : {};

  return (
    <ContainerComponent
      {...containerProps}
      style={[
        styles.container,
        LinearGradient === View ? styles.fallbackContainer : {}
      ]}
    >
      {/* Background animated circles */}
      <Animated.View style={[styles.backgroundCircle1, { opacity: fadeAnim }]}>
        {LinearGradient !== View ? (
          <LinearGradient
            colors={['#60A5FA', '#8B5CF6']}
            style={styles.circle}
          />
        ) : (
          <View style={[styles.circle, { backgroundColor: '#60A5FA' }]} />
        )}
      </Animated.View>
      <Animated.View style={[styles.backgroundCircle2, { opacity: fadeAnim }]}>
        {LinearGradient !== View ? (
          <LinearGradient
            colors={['#22D3EE', '#3B82F6']}
            style={styles.circle}
          />
        ) : (
          <View style={[styles.circle, { backgroundColor: '#22D3EE' }]} />
        )}
      </Animated.View>

      {/* Main content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* App Icon with Background Gradient */}
        <BackgroundGradient>
          <View style={styles.iconContainer}>
            <Animated.View
              style={{
                transform: [{ rotate: iconRotate }],
              }}
            >
              {Ionicons ? (
                <Ionicons name="cut" size={64} color="#60A5FA" />
              ) : (
                <View style={styles.fallbackIcon}>
                  <Text style={styles.fallbackIconText}>✂️</Text>
                </View>
              )}
            </Animated.View>
          </View>
        </BackgroundGradient>

        {/* App Name */}
        <Animated.Text
          style={[
            styles.appName,
            {
              textShadowColor: `rgba(59, 130, 246, ${textShadowOpacity})`,
            },
          ]}
        >
          Clipsy
        </Animated.Text>

        {/* Wave Animation */}
        <WaveAnimation />

        {/* Loading Progress */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressWidth,
                  backgroundColor: LinearGradient === View ? '#60A5FA' : undefined,
                },
              ]}
            >
              {LinearGradient !== View ? (
                <LinearGradient
                  colors={['#60A5FA', '#22D3EE']}
                  style={styles.progressGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                />
              ) : null}
            </Animated.View>
          </View>
          <Animated.Text
            style={[
              styles.progressText,
              {
                opacity: textShadowOpacity,
              },
            ]}
          >
            Loading... {loadingProgress}%
          </Animated.Text>
        </View>

        {/* Tagline */}
        <Text style={styles.tagline}>
          Your clipboard companion for seamless sync
        </Text>
      </Animated.View>

      {/* Bottom decorative dots */}
      <View style={styles.bottomDots}>
        {[0, 1, 2].map((index) => (
          <View key={index} style={styles.dot} />
        ))}
      </View>
    </ContainerComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundCircle1: {
    position: 'absolute',
    top: height * 0.2,
    left: width * 0.1,
    width: 200,
    height: 200,
    borderRadius: 100,
    opacity: 0.3,
  },
  backgroundCircle2: {
    position: 'absolute',
    bottom: height * 0.2,
    right: width * 0.1,
    width: 200,
    height: 200,
    borderRadius: 100,
    opacity: 0.3,
  },
  circle: {
    flex: 1,
    borderRadius: 100,
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  gradientContainer: {
    position: 'relative',
    marginBottom: 32,
  },
  gradientBackground: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    bottom: -8,
    borderRadius: 24,
    opacity: 0.6,
  },
  gradient: {
    flex: 1,
    borderRadius: 24,
  },
  gradientContent: {
    position: 'relative',
    zIndex: 10,
  },
  iconContainer: {
    backgroundColor: '#1E293B',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  appName: {
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 32,
    textAlign: 'center',
    letterSpacing: 2,
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
  },
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    height: 32,
  },
  waveBar: {
    width: 8,
    height: 32,
    marginHorizontal: 2,
    borderRadius: 4,
    overflow: 'hidden',
  },
  waveGradient: {
    flex: 1,
    borderRadius: 4,
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  progressBar: {
    width: 256,
    height: 8,
    backgroundColor: '#334155',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressGradient: {
    flex: 1,
    borderRadius: 4,
  },
  progressText: {
    color: '#CBD5E1',
    fontSize: 14,
    fontWeight: '500',
  },
  tagline: {
    color: '#94A3B8',
    fontSize: 16,
    textAlign: 'center',
    maxWidth: 300,
    lineHeight: 24,
  },
  bottomDots: {
    position: 'absolute',
    bottom: 32,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    backgroundColor: '#60A5FA',
    borderRadius: 4,
    marginHorizontal: 4,
    opacity: 0.5,
  },
  fallbackContainer: {
    backgroundColor: '#0F172A',
  },
  fallbackIcon: {
    width: 64,
    height: 64,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fallbackIconText: {
    fontSize: 48,
    color: '#60A5FA',
  },
});

export default ClipsyLoadingScreen;
