/**
 * Web bundle optimization utilities
 * Use these helpers to reduce bundle size for web builds
 */
import { Platform } from 'react-native';

/**
 * Conditionally loads a module only in web environment
 * @param {Function} importFn - Dynamic import function
 * @returns {Promise<any>|null} - Module or null on non-web platforms
 */
export const webOnlyImport = (importFn) => {
  if (Platform.OS === 'web') {
    return importFn();
  }
  return Promise.resolve(null);
};

/**
 * Removes properties not needed for web from objects
 * Use this to reduce the size of large data objects
 * @param {Object} obj - Object to optimize
 * @param {Array<string>} keysToKeep - Keys to preserve
 * @returns {Object} - Optimized object for web
 */
export const optimizeForWeb = (obj, keysToKeep) => {
  if (Platform.OS !== 'web') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => optimizeForWeb(item, keysToKeep));
  }
  
  if (obj && typeof obj === 'object') {
    const result = {};
    
    if (keysTo<PERSON>eep) {
      // Only keep specified keys
      keysToKeep.forEach(key => {
        if (obj.hasOwnProperty(key)) {
          result[key] = obj[key];
        }
      });
    } else {
      // Keep all keys but process nested objects
      Object.keys(obj).forEach(key => {
        result[key] = optimizeForWeb(obj[key]);
      });
    }
    
    return result;
  }
  
  return obj;
};
