import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity, Dimensions } from 'react-native';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Camera } from 'expo-camera';

interface ConnectionInfo {
  type: string;
  ip: string;
  port: number;
  token: string;
  device_id: string;
  timestamp: number;
}

interface QRScannerProps {
  onConnectionInfo: (info: ConnectionInfo) => void;
  onClose: () => void;
}

const QRScanner: React.FC<QRScannerProps> = ({ onConnectionInfo, onClose }) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [isActive, setIsActive] = useState(true);

  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === 'granted');
    };

    getBarCodeScannerPermissions();
  }, []);

  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    if (scanned) return;
    
    setScanned(true);
    setIsActive(false);

    try {
      const connectionInfo: ConnectionInfo = JSON.parse(data);
      
      // Validate the QR code data
      if (connectionInfo.type !== 'clipsync_pairing') {
        Alert.alert(
          'Invalid QR Code',
          'This QR code is not for ClipSync device pairing.',
          [{ text: 'OK', onPress: () => resetScanner() }]
        );
        return;
      }

      // Check if the QR code is not too old (5 minutes)
      const now = Date.now();
      const qrAge = now - connectionInfo.timestamp;
      const maxAge = 5 * 60 * 1000; // 5 minutes

      if (qrAge > maxAge) {
        Alert.alert(
          'QR Code Expired',
          'This QR code has expired. Please generate a new one on your desktop.',
          [{ text: 'OK', onPress: () => resetScanner() }]
        );
        return;
      }

      // Valid QR code - proceed with connection
      Alert.alert(
        'Device Found',
        `Connect to ClipSync desktop at ${connectionInfo.ip}:${connectionInfo.port}?`,
        [
          { text: 'Cancel', onPress: () => resetScanner() },
          { 
            text: 'Connect', 
            onPress: () => {
              onConnectionInfo(connectionInfo);
              onClose();
            }
          }
        ]
      );

    } catch (error) {
      Alert.alert(
        'Invalid QR Code',
        'Could not parse QR code data. Please try again.',
        [{ text: 'OK', onPress: () => resetScanner() }]
      );
    }
  };

  const resetScanner = () => {
    setScanned(false);
    setIsActive(true);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>No access to camera</Text>
        <TouchableOpacity style={styles.button} onPress={onClose}>
          <Text style={styles.buttonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📱 Scan QR Code</Text>
        <Text style={styles.subtitle}>Point your camera at the QR code on your desktop</Text>
      </View>

      <View style={styles.cameraContainer}>
        <BarCodeScanner
          onBarCodeScanned={isActive ? handleBarCodeScanned : undefined}
          style={styles.camera}
        />
        
        {/* Scanning overlay */}
        <View style={styles.overlay}>
          <View style={styles.scanArea}>
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
          </View>
        </View>

        {scanned && (
          <View style={styles.scannedOverlay}>
            <Text style={styles.scannedText}>✅ QR Code Detected</Text>
          </View>
        )}
      </View>

      <View style={styles.controls}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>❌ Close</Text>
        </TouchableOpacity>
        
        {scanned && (
          <TouchableOpacity style={styles.rescanButton} onPress={resetScanner}>
            <Text style={styles.rescanButtonText}>🔄 Scan Again</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>📋 Instructions:</Text>
        <Text style={styles.instructionText}>1. Open ClipSync on your desktop</Text>
        <Text style={styles.instructionText}>2. Go to Settings → Device Pairing</Text>
        <Text style={styles.instructionText}>3. Click "Start Pairing Server"</Text>
        <Text style={styles.instructionText}>4. Point your camera at the QR code</Text>
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get('window');
const scanAreaSize = Math.min(width * 0.7, 250);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#ccc',
    textAlign: 'center',
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: scanAreaSize,
    height: scanAreaSize,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#00ff00',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scannedOverlay: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 20,
  },
  scannedText: {
    color: '#00ff00',
    fontSize: 18,
    fontWeight: 'bold',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  closeButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  rescanButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  rescanButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  instructions: {
    padding: 20,
    backgroundColor: 'rgba(0,0,0,0.9)',
  },
  instructionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  instructionText: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 5,
  },
  message: {
    textAlign: 'center',
    paddingBottom: 10,
    color: '#fff',
    fontSize: 16,
  },
  button: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignSelf: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default QRScanner;
